"""
疗愈路径生成器
根据情感分析生成个性化疗愈路径
"""

import random
from typing import Dict, List
import logging
from .music_recommender import MusicRecommender

logger = logging.getLogger(__name__)

class HealingGenerator:
    """疗愈路径生成器"""
    
    def __init__(self):
        """初始化疗愈生成器"""
        self.healing_content = self._load_healing_content()
        self.music_recommender = MusicRecommender()
    
    def _load_healing_content(self) -> Dict:
        """加载疗愈内容库"""
        return {
            'music': {
                'sad': [
                    {'title': '月光奏鸣曲', 'artist': '贝多芬', 'type': '古典', 'mood': '宁静'},
                    {'title': 'Mad World', 'artist': '<PERSON>', 'type': '氛围', 'mood': '深沉'},
                    {'title': '夜的钢琴曲五', 'artist': '石进', 'type': '钢琴', 'mood': '治愈'},
                    {'title': 'Spiegel im Spiegel', 'artist': '<PERSON>r<PERSON>rt', 'type': '现代古典', 'mood': '纯净'},
                ],
                'angry': [
                    {'title': '雨声', 'artist': '自然音效', 'type': '自然', 'mood': '平静'},
                    {'title': 'Weightless', 'artist': 'Marconi Union', 'type': '氛围', 'mood': '放松'},
                    {'title': '禅修音乐', 'artist': '冥想大师', 'type': '冥想', 'mood': '宁静'},
                    {'title': 'Claire de Lune', 'artist': '德彪西', 'type': '古典', 'mood': '优雅'},
                ],
                'anxious': [
                    {'title': '海浪声', 'artist': '自然音效', 'type': '自然', 'mood': '舒缓'},
                    {'title': 'Deep Jungle Walk', 'artist': 'Astrix', 'type': '氛围', 'mood': '沉浸'},
                    {'title': '森林晨曲', 'artist': '自然录音', 'type': '自然', 'mood': '清新'},
                    {'title': 'Gymnopédie No.1', 'artist': 'Erik Satie', 'type': '古典', 'mood': '简约'},
                ],
                'lonely': [
                    {'title': 'The Blue Notebooks', 'artist': 'Max Richter', 'type': '现代古典', 'mood': '温暖'},
                    {'title': '星空', 'artist': '二胡独奏', 'type': '民族', 'mood': '深情'},
                    {'title': 'Elegy for Dunkirk', 'artist': 'Dario Marianelli', 'type': '电影配乐', 'mood': '史诗'},
                    {'title': '夜莺', 'artist': '古筝演奏', 'type': '民族', 'mood': '优美'},
                ],
                'happy': [
                    {'title': 'Ludovico Einaudi - Nuvole Bianche', 'artist': 'Ludovico Einaudi', 'type': '现代古典', 'mood': '升华'},
                    {'title': '春天的故事', 'artist': '钢琴版', 'type': '钢琴', 'mood': '温暖'},
                    {'title': 'Comptine d\'un autre été', 'artist': 'Yann Tiersen', 'type': '电影配乐', 'mood': '浪漫'},
                    {'title': '阳光总在风雨后', 'artist': '纯音乐版', 'type': '励志', 'mood': '希望'},
                ]
            },
            'meditation': {
                'deep_healing': [
                    "闭上眼睛，深深吸气，想象你正站在一片宁静的湖边。湖水如镜，倒映着你内心的平静。",
                    "感受你的呼吸，每一次吸气都带来新的希望，每一次呼气都释放内心的重负。",
                    "想象有一束温暖的光芒从天空洒下，轻柔地包围着你，治愈着你内心的伤痛。",
                    "让自己沉浸在这份宁静中，告诉自己：'我值得被爱，我值得快乐。'"
                ],
                'comfort': [
                    "轻轻地将手放在心脏上，感受它稳定的跳动，这是生命的节拍，也是希望的节拍。",
                    "想象你最爱的人正在身边，给你一个温暖的拥抱，告诉你一切都会好起来的。",
                    "深呼吸三次，每次呼气时，想象你正在释放今天的疲惫和烦恼。",
                    "提醒自己：今天的困难是暂时的，明天的太阳依然会升起。"
                ],
                'calming': [
                    "从脚趾开始，逐渐放松身体的每一个部位，直到头顶，让紧张完全消散。",
                    "想象你正躺在柔软的云朵上，随着微风轻柔地飘荡，没有任何压力。",
                    "专注于你的呼吸，数到十，让每一个数字都带走一分愤怒或焦虑。",
                    "告诉自己：'我选择平静，我选择放下，我选择内心的和谐。'"
                ],
                'energizing': [
                    "想象清晨的第一缕阳光照在你的脸上，给你带来新的活力和希望。",
                    "深深吸气，想象你正在吸入大自然的能量，让它充满你的每一个细胞。",
                    "伸展你的身体，感受肌肉的力量，提醒自己你比想象中更强大。",
                    "设想今天有一件美好的事情等待着你，让期待点燃你内心的火焰。"
                ],
                'clarity': [
                    "想象你的思绪像云雾一样慢慢散去，露出清澈的蓝天。",
                    "问问自己：'现在最重要的是什么？'让答案自然地浮现在心中。",
                    "想象你站在山顶，俯瞰着下面的世界，一切都变得清晰明了。",
                    "相信你内心的智慧，它会指引你找到正确的方向。"
                ]
            },
            'quotes': {
                'inspirational': [
                    "黑夜给了我黑色的眼睛，我却用它寻找光明。",
                    "每一个不曾起舞的日子，都是对生命的辜负。",
                    "生活不是等待暴风雨过去，而是学会在雨中起舞。",
                    "你的心就像一片海洋，深邃而宽广，足以包容一切。",
                    "星星之所以美丽，是因为它们在黑暗中闪耀。",
                    "The darkest nights produce the brightest stars.",
                    "In the middle of difficulty lies opportunity.",
                    "You are braver than you believe, stronger than you seem."
                ],
                'healing': [
                    "治愈不是忘记痛苦，而是学会与它和平共处。",
                    "每一次呼吸都是一个新的开始。",
                    "你的伤痛会成为你的力量。",
                    "时间不会治愈一切，但它会教会我们如何与痛苦共存。",
                    "在你的心中，永远有一片净土等待着你。",
                    "Healing is not about forgetting, it's about remembering without pain.",
                    "Your scars are proof that you survived.",
                    "Every sunset brings the promise of a new dawn."
                ],
                'hope': [
                    "希望是黑暗中的一盏明灯。",
                    "只要心中有光，就不怕路途黑暗。",
                    "明天的太阳依然会升起。",
                    "每一个结束都是新的开始。",
                    "相信美好的事情即将发生。",
                    "Hope is the thing with feathers that perches in the soul.",
                    "Even the darkest night will end and the sun will rise.",
                    "Tomorrow is the first day of the rest of your life."
                ]
            },
            'activities': {
                'creative': [
                    "画一幅表达你当前心情的抽象画，不需要完美，只需要真实。",
                    "写一封给未来自己的信，告诉TA你现在的感受和希望。",
                    "用粘土或橡皮泥捏一个代表你内心状态的小雕塑。",
                    "创作一首小诗，描述你希望拥有的心境。"
                ],
                'physical': [
                    "到户外走走，感受新鲜空气和阳光。",
                    "做10分钟简单的伸展运动。",
                    "尝试瑜伽的婴儿式，让身心完全放松。",
                    "洗一个温暖的澡，想象洗去所有的负面情绪。"
                ],
                'social': [
                    "给一个你关心的人发一条温暖的消息。",
                    "回忆一个让你感到被爱的时刻。",
                    "想象你正在安慰一个朋友，然后用同样的话安慰自己。",
                    "写下三个你感激的人，以及感激他们的原因。"
                ],
                'mindful': [
                    "专注地品尝一杯茶或咖啡，感受每一个细节。",
                    "观察窗外的景色5分钟，注意光线和色彩的变化。",
                    "听一段自然声音，想象自己身处其中。",
                    "闻一种你喜欢的香味，让它唤起美好的回忆。"
                ]
            },
            'visual_elements': {
                'colors': {
                    'calming': ['#87CEEB', '#E6E6FA', '#F0F8FF', '#B0E0E6'],
                    'energizing': ['#FFD700', '#FFA500', '#FF6347', '#FF69B4'],
                    'healing': ['#98FB98', '#90EE90', '#00FA9A', '#7FFFD4'],
                    'peaceful': ['#DDA0DD', '#D8BFD8', '#E0E0E0', '#F5F5DC']
                },
                'symbols': {
                    'hope': ['🌅', '🌟', '🕊️', '🌈'],
                    'peace': ['🕯️', '🧘', '☮️', '🌸'],
                    'growth': ['🌱', '🌳', '🦋', '🌻'],
                    'love': ['💝', '🤗', '💖', '🌹']
                }
            }
        }
    
    def generate_healing_path(self, emotion_analysis: Dict) -> Dict:
        """
        生成个性化疗愈路径
        
        Args:
            emotion_analysis: 情感分析结果
            
        Returns:
            疗愈路径字典
        """
        dominant_emotion = emotion_analysis['dominant_emotion']
        emotion_score = emotion_analysis['emotion_score']
        sentiment = emotion_analysis['sentiment']
        
        # 确定疗愈类型
        healing_type = self._determine_healing_type(dominant_emotion, emotion_score)
        
        # 生成疗愈路径
        healing_path = {
            'type': healing_type,
            'emotion': dominant_emotion,
            'intensity': self._get_intensity_level(emotion_score),
            'music': self._select_music(dominant_emotion),
            'meditation': self._select_meditation(healing_type),
            'quote': self._select_quote(sentiment),
            'activity': self._select_activity(dominant_emotion),
            'visual': self._select_visual_elements(dominant_emotion),
            'duration': self._estimate_duration(emotion_score),
            'steps': self._generate_steps(healing_type, dominant_emotion)
        }
        
        return healing_path
    
    def _determine_healing_type(self, emotion: str, score: float) -> str:
        """确定疗愈类型"""
        intensity = abs(score)
        
        if emotion in ['sad', 'lonely']:
            return 'deep_healing' if intensity > 0.6 else 'comfort'
        elif emotion in ['angry', 'anxious']:
            return 'calming'
        elif emotion == 'tired':
            return 'energizing'
        elif emotion == 'confused':
            return 'clarity'
        else:
            return 'comfort'
    
    def _get_intensity_level(self, score: float) -> str:
        """获取强度等级"""
        intensity = abs(score)
        if intensity > 0.7:
            return 'high'
        elif intensity > 0.4:
            return 'medium'
        else:
            return 'low'
    
    def _select_music(self, emotion: str) -> Dict:
        """选择音乐"""
        recommendations = self.music_recommender.get_music_recommendations_with_links(emotion, 1)
        if recommendations:
            return recommendations[0]
        else:
            # 备用音乐
            music_list = self.healing_content['music'].get(emotion,
                        self.healing_content['music']['sad'])
            return random.choice(music_list)
    
    def _select_meditation(self, healing_type: str) -> str:
        """选择冥想指导"""
        meditations = self.healing_content['meditation'].get(healing_type,
                     self.healing_content['meditation']['comfort'])
        return random.choice(meditations)
    
    def _select_quote(self, sentiment: str) -> str:
        """选择励志语句"""
        if sentiment == 'negative':
            quote_type = 'healing'
        elif sentiment == 'neutral':
            quote_type = 'hope'
        else:
            quote_type = 'inspirational'
        
        quotes = self.healing_content['quotes'][quote_type]
        return random.choice(quotes)
    
    def _select_activity(self, emotion: str) -> str:
        """选择活动建议"""
        if emotion in ['sad', 'lonely']:
            activity_type = 'social'
        elif emotion in ['angry', 'anxious']:
            activity_type = 'mindful'
        elif emotion == 'tired':
            activity_type = 'physical'
        else:
            activity_type = 'creative'
        
        activities = self.healing_content['activities'][activity_type]
        return random.choice(activities)
    
    def _select_visual_elements(self, emotion: str) -> Dict:
        """选择视觉元素"""
        if emotion in ['sad', 'lonely']:
            color_theme = 'healing'
            symbol_theme = 'hope'
        elif emotion in ['angry', 'anxious']:
            color_theme = 'calming'
            symbol_theme = 'peace'
        elif emotion == 'tired':
            color_theme = 'energizing'
            symbol_theme = 'growth'
        else:
            color_theme = 'peaceful'
            symbol_theme = 'love'
        
        colors = self.healing_content['visual_elements']['colors'][color_theme]
        symbols = self.healing_content['visual_elements']['symbols'][symbol_theme]
        
        return {
            'colors': random.sample(colors, 2),
            'symbols': random.sample(symbols, 2),
            'theme': color_theme
        }
    
    def _estimate_duration(self, score: float) -> int:
        """估算疗愈时长（分钟）"""
        intensity = abs(score)
        if intensity > 0.7:
            return 15
        elif intensity > 0.4:
            return 10
        else:
            return 5
    
    def _generate_steps(self, healing_type: str, emotion: str) -> List[str]:
        """生成疗愈步骤"""
        base_steps = [
            "找一个安静舒适的地方坐下",
            "深呼吸三次，让自己放松",
            "播放推荐的音乐",
            "跟随冥想指导进行正念练习",
            "阅读励志语句，让它在心中回响",
            "尝试建议的活动",
            "记录你的感受变化"
        ]
        
        # 根据疗愈类型调整步骤
        if healing_type == 'deep_healing':
            base_steps.insert(3, "允许自己感受情绪，不要压抑")
            base_steps.append("给自己一些时间慢慢恢复")
        elif healing_type == 'calming':
            base_steps.insert(2, "专注于放慢呼吸节奏")
            base_steps.append("提醒自己：这种感觉会过去的")
        elif healing_type == 'energizing':
            base_steps.insert(4, "想象阳光充满你的身体")
            base_steps.append("为今天设定一个小目标")
        
        return base_steps
    
    def get_emergency_support(self) -> Dict:
        """获取紧急支持信息"""
        return {
            'message': '如果你感到极度痛苦或有自伤想法，请立即寻求专业帮助。',
            'hotlines': [
                {'name': '心理危机干预热线', 'number': '400-161-9995'},
                {'name': '北京危机干预热线', 'number': '400-161-9995'},
                {'name': '上海心理援助热线', 'number': '021-64383562'}
            ],
            'immediate_actions': [
                '深呼吸，数到十',
                '联系信任的朋友或家人',
                '去到安全的地方',
                '拨打心理援助热线',
                '如有必要，前往最近的医院'
            ]
        }
