"""
情感分析模块
用于分析用户输入的文本情感
"""

import nltk
from textblob import TextBlob
import re
from typing import Dict, List, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmotionAnalyzer:
    """情感分析器"""
    
    def __init__(self):
        """初始化情感分析器"""
        self.download_nltk_data()
        
        # 中文情感词典
        self.positive_words = {
            '开心', '快乐', '高兴', '愉快', '兴奋', '满足', '幸福', '喜悦',
            '舒适', '轻松', '平静', '安心', '温暖', '感激', '希望', '乐观',
            '自信', '勇敢', '坚强', '成功', '胜利', '美好', '完美', '棒',
            '好', '不错', '优秀', '出色', '精彩', 'wonderful', 'great', 'happy',
            'joy', 'love', 'peace', 'calm', 'hope', 'confident'
        }
        
        self.negative_words = {
            '难过', '伤心', '痛苦', '悲伤', '沮丧', '失望', '绝望', '孤独',
            '焦虑', '担心', '害怕', '恐惧', '愤怒', '生气', '烦躁', '疲惫',
            '累', '压力', '紧张', '困惑', '迷茫', '无助', '空虚', '麻木',
            '后悔', '内疚', '羞愧', '嫉妒', '讨厌', '厌恶', '恶心', '糟糕',
            '坏', '差', '失败', 'sad', 'angry', 'fear', 'anxiety', 'depression',
            'lonely', 'tired', 'stress', 'worry', 'hate', 'terrible'
        }
        
        # 情感强度词
        self.intensity_words = {
            '非常': 2.0, '特别': 2.0, '极其': 2.5, '超级': 2.0, '十分': 1.8,
            '很': 1.5, '挺': 1.3, '比较': 1.2, '有点': 0.8, '稍微': 0.7,
            '一点': 0.6, '不太': 0.5, '不': 0.1, '没有': 0.1
        }
    
    def download_nltk_data(self):
        """下载必要的NLTK数据"""
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        try:
            nltk.data.find('corpora/vader_lexicon')
        except LookupError:
            nltk.download('vader_lexicon')
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除特殊字符，保留中文、英文、数字和基本标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s.,!?;:]', '', text)
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def analyze_emotion(self, text: str) -> Dict:
        """
        分析文本情感
        
        Args:
            text: 输入文本
            
        Returns:
            包含情感分析结果的字典
        """
        if not text or not text.strip():
            return {
                'sentiment': 'neutral',
                'polarity': 0.0,
                'subjectivity': 0.0,
                'emotion_score': 0.0,
                'dominant_emotion': 'neutral',
                'emotions': {},
                'keywords': []
            }
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        
        # 使用TextBlob进行基础情感分析
        blob = TextBlob(cleaned_text)
        polarity = blob.sentiment.polarity
        subjectivity = blob.sentiment.subjectivity
        
        # 中文情感分析
        chinese_sentiment = self._analyze_chinese_sentiment(cleaned_text)
        
        # 综合情感分数
        emotion_score = (polarity + chinese_sentiment['score']) / 2
        
        # 确定主导情感
        if emotion_score > 0.1:
            sentiment = 'positive'
            dominant_emotion = 'happy'
        elif emotion_score < -0.1:
            sentiment = 'negative'
            dominant_emotion = self._get_dominant_negative_emotion(cleaned_text)
        else:
            sentiment = 'neutral'
            dominant_emotion = 'neutral'
        
        # 提取关键词
        keywords = self._extract_keywords(cleaned_text)
        
        # 详细情感分析
        emotions = self._analyze_detailed_emotions(cleaned_text)
        
        return {
            'sentiment': sentiment,
            'polarity': polarity,
            'subjectivity': subjectivity,
            'emotion_score': emotion_score,
            'dominant_emotion': dominant_emotion,
            'emotions': emotions,
            'keywords': keywords,
            'chinese_analysis': chinese_sentiment
        }
    
    def _analyze_chinese_sentiment(self, text: str) -> Dict:
        """分析中文情感"""
        # 对中文文本进行字符级别的分析
        positive_count = 0
        negative_count = 0
        intensity_modifier = 1.0

        # 检查强度词
        for intensity_word, modifier in self.intensity_words.items():
            if intensity_word in text:
                intensity_modifier = max(intensity_modifier, modifier)

        # 检查情感词
        for word in self.positive_words:
            if word in text:
                positive_count += intensity_modifier

        for word in self.negative_words:
            if word in text:
                negative_count += intensity_modifier

        total_count = positive_count + negative_count
        if total_count == 0:
            score = 0.0
        else:
            score = (positive_count - negative_count) / total_count

        return {
            'score': score,
            'positive_count': positive_count,
            'negative_count': negative_count
        }
    
    def _get_dominant_negative_emotion(self, text: str) -> str:
        """获取主导的负面情感"""
        emotion_keywords = {
            'sad': ['难过', '伤心', '悲伤', '失望', '沮丧', 'sad', 'sorrow'],
            'angry': ['愤怒', '生气', '烦躁', '讨厌', 'angry', 'mad', 'hate'],
            'anxious': ['焦虑', '担心', '紧张', '害怕', 'anxiety', 'worry', 'fear'],
            'lonely': ['孤独', '寂寞', '空虚', 'lonely', 'empty'],
            'tired': ['疲惫', '累', '疲劳', 'tired', 'exhausted'],
            'confused': ['困惑', '迷茫', '不知道', 'confused', 'lost']
        }
        
        emotion_scores = {}
        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                emotion_scores[emotion] = score
        
        if emotion_scores:
            return max(emotion_scores, key=emotion_scores.get)
        return 'sad'
    
    def _analyze_detailed_emotions(self, text: str) -> Dict:
        """详细情感分析"""
        emotions = {
            'joy': 0.0,
            'sadness': 0.0,
            'anger': 0.0,
            'fear': 0.0,
            'surprise': 0.0,
            'disgust': 0.0,
            'trust': 0.0,
            'anticipation': 0.0
        }
        
        # 基于关键词的情感检测
        emotion_patterns = {
            'joy': ['开心', '快乐', '高兴', '愉快', '兴奋', '幸福', 'happy', 'joy'],
            'sadness': ['难过', '伤心', '悲伤', '失望', '沮丧', 'sad', 'sorrow'],
            'anger': ['愤怒', '生气', '烦躁', '讨厌', 'angry', 'mad'],
            'fear': ['害怕', '恐惧', '担心', '焦虑', 'fear', 'anxiety'],
            'surprise': ['惊讶', '意外', '震惊', 'surprise', 'shock'],
            'disgust': ['厌恶', '恶心', '讨厌', 'disgust', 'hate'],
            'trust': ['信任', '相信', '依赖', 'trust', 'believe'],
            'anticipation': ['期待', '希望', '盼望', 'hope', 'expect']
        }
        
        for emotion, patterns in emotion_patterns.items():
            score = sum(0.1 for pattern in patterns if pattern in text)
            emotions[emotion] = min(score, 1.0)
        
        return emotions
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        keywords = []

        # 检查情感词
        for word in self.positive_words:
            if word in text:
                keywords.append(word)

        for word in self.negative_words:
            if word in text:
                keywords.append(word)

        # 检查强度词
        for word in self.intensity_words:
            if word in text:
                keywords.append(word)

        return list(set(keywords))[:10]  # 返回前10个关键词
    
    def get_emotion_intensity(self, analysis_result: Dict) -> str:
        """获取情感强度描述"""
        score = abs(analysis_result['emotion_score'])
        
        if score >= 0.7:
            return '强烈'
        elif score >= 0.4:
            return '中等'
        elif score >= 0.1:
            return '轻微'
        else:
            return '平静'
    
    def suggest_healing_type(self, analysis_result: Dict) -> str:
        """根据情感分析建议疗愈类型"""
        dominant_emotion = analysis_result['dominant_emotion']
        intensity = self.get_emotion_intensity(analysis_result)
        
        if dominant_emotion in ['sad', 'lonely']:
            if intensity == '强烈':
                return 'deep_healing'
            else:
                return 'comfort'
        elif dominant_emotion in ['angry', 'anxious']:
            return 'calming'
        elif dominant_emotion == 'tired':
            return 'energizing'
        elif dominant_emotion == 'confused':
            return 'clarity'
        else:
            return 'general'
