"""
视觉效果模块
实现记忆处理的视觉效果
"""

import streamlit as st
import time
import random
from typing import Dict, List

class VisualEffects:
    """视觉效果管理器"""
    
    def __init__(self):
        """初始化视觉效果管理器"""
        self.colors = {
            'seal': ['#4A90E2', '#357ABD', '#2E5C8A'],
            'release': ['#FFD700', '#FFA500', '#FF6347'],
            'destroy': ['#FF4444', '#CC0000', '#990000'],
            'store': ['#4CAF50', '#45A049', '#3D8B40']
        }
    
    def show_memory_seal_effect(self):
        """显示记忆封存效果"""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <div style="
                background: linear-gradient(45deg, #4A90E2, #357ABD);
                border-radius: 50%;
                width: 100px;
                height: 100px;
                margin: 0 auto 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                animation: pulse 2s infinite;
            ">
                📦
            </div>
            <h3 style="color: #4A90E2;">记忆已被安全封存</h3>
            <p style="color: #666;">它将在你的内心深处静静沉睡，直到你准备好面对它...</p>
        </div>
        
        <style>
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        </style>
        """, unsafe_allow_html=True)
        
        # 添加粒子效果
        self._show_particle_effect("seal")
        
        # 显示气球效果
        st.balloons()
    
    def show_memory_release_effect(self):
        """显示记忆放飞效果"""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <div style="
                background: linear-gradient(45deg, #FFD700, #FFA500);
                border-radius: 50%;
                width: 100px;
                height: 100px;
                margin: 0 auto 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                animation: float 3s ease-in-out infinite;
            ">
                🎈
            </div>
            <h3 style="color: #FFA500;">记忆如孔明灯般飞向夜空</h3>
            <p style="color: #666;">带走了你的烦恼，留下了内心的平静...</p>
        </div>
        
        <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        </style>
        """, unsafe_allow_html=True)
        
        # 显示气球效果
        st.balloons()
        
        # 添加上升效果
        self._show_rising_effect()
    
    def show_memory_destroy_effect(self):
        """显示记忆粉碎效果"""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <div style="
                background: linear-gradient(45deg, #FF4444, #CC0000);
                border-radius: 50%;
                width: 100px;
                height: 100px;
                margin: 0 auto 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                animation: shake 0.5s infinite;
            ">
                🗑️
            </div>
            <h3 style="color: #FF4444;">记忆已被彻底粉碎</h3>
            <p style="color: #666;">就像从未存在过一样，你获得了新的开始...</p>
        </div>
        
        <style>
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        </style>
        """, unsafe_allow_html=True)
        
        # 显示雪花效果
        st.snow()
        
        # 添加粉碎效果
        self._show_shatter_effect()
    
    def show_memory_store_effect(self):
        """显示记忆保存效果"""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <div style="
                background: linear-gradient(45deg, #4CAF50, #45A049);
                border-radius: 50%;
                width: 100px;
                height: 100px;
                margin: 0 auto 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                animation: glow 2s ease-in-out infinite alternate;
            ">
                💾
            </div>
            <h3 style="color: #4CAF50;">记忆已安全保存</h3>
            <p style="color: #666;">你可以随时回顾这段珍贵的经历...</p>
        </div>
        
        <style>
        @keyframes glow {
            from { box-shadow: 0 0 10px #4CAF50; }
            to { box-shadow: 0 0 20px #4CAF50, 0 0 30px #4CAF50; }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def _show_particle_effect(self, effect_type: str):
        """显示粒子效果"""
        colors = self.colors.get(effect_type, self.colors['store'])
        
        particles_html = """
        <div id="particles-container" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1000;">
        """
        
        for i in range(20):
            color = random.choice(colors)
            left = random.randint(0, 100)
            delay = random.uniform(0, 2)
            duration = random.uniform(2, 4)
            
            particles_html += f"""
            <div style="
                position: absolute;
                left: {left}%;
                top: 100%;
                width: 4px;
                height: 4px;
                background: {color};
                border-radius: 50%;
                animation: particle-rise {duration}s ease-out {delay}s forwards;
            "></div>
            """
        
        particles_html += """
        </div>
        
        <style>
        @keyframes particle-rise {
            to {
                transform: translateY(-100vh);
                opacity: 0;
            }
        }
        </style>
        
        <script>
        setTimeout(() => {
            const container = document.getElementById('particles-container');
            if (container) container.remove();
        }, 5000);
        </script>
        """
        
        st.markdown(particles_html, unsafe_allow_html=True)
    
    def _show_rising_effect(self):
        """显示上升效果"""
        st.markdown("""
        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1000;">
            <div style="
                font-size: 3rem;
                animation: rise-and-fade 4s ease-out forwards;
            ">
                🎈✨🌟
            </div>
        </div>
        
        <style>
        @keyframes rise-and-fade {
            0% {
                transform: translate(-50%, -50%) translateY(0);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) translateY(-200px);
                opacity: 0;
            }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def _show_shatter_effect(self):
        """显示粉碎效果"""
        st.markdown("""
        <div id="shatter-container" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1000;">
        """, unsafe_allow_html=True)
        
        # 创建粉碎片段
        for i in range(15):
            left = random.randint(10, 90)
            top = random.randint(10, 90)
            rotation = random.randint(0, 360)
            scale = random.uniform(0.5, 1.5)
            
            st.markdown(f"""
            <div style="
                position: absolute;
                left: {left}%;
                top: {top}%;
                width: 10px;
                height: 10px;
                background: #FF4444;
                transform: rotate({rotation}deg) scale({scale});
                animation: shatter-fall 2s ease-in forwards;
            "></div>
            """, unsafe_allow_html=True)
        
        st.markdown("""
        </div>
        
        <style>
        @keyframes shatter-fall {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(100vh) rotate(720deg);
            }
        }
        </style>
        
        <script>
        setTimeout(() => {
            const container = document.getElementById('shatter-container');
            if (container) container.remove();
        }, 3000);
        </script>
        """, unsafe_allow_html=True)
    
    def show_emotion_visualization(self, emotion_data: Dict):
        """显示情感可视化"""
        emotion_colors = {
            'happy': '#FFD700',
            'sad': '#4169E1',
            'angry': '#FF4500',
            'anxious': '#9370DB',
            'lonely': '#708090',
            'tired': '#8B4513',
            'confused': '#696969',
            'neutral': '#A9A9A9'
        }
        
        dominant_emotion = emotion_data['dominant_emotion']
        color = emotion_colors.get(dominant_emotion, '#A9A9A9')
        intensity = abs(emotion_data['emotion_score'])
        
        st.markdown(f"""
        <div style="text-align: center; padding: 2rem;">
            <div style="
                background: radial-gradient(circle, {color}40, {color}20);
                border: 3px solid {color};
                border-radius: 50%;
                width: {100 + intensity * 100}px;
                height: {100 + intensity * 100}px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: {2 + intensity}rem;
                animation: emotion-pulse 2s ease-in-out infinite;
            ">
                {self._get_emotion_emoji(dominant_emotion)}
            </div>
        </div>
        
        <style>
        @keyframes emotion-pulse {{
            0%, 100% {{ transform: scale(1); }}
            50% {{ transform: scale({1 + intensity * 0.2}); }}
        }}
        </style>
        """, unsafe_allow_html=True)
    
    def _get_emotion_emoji(self, emotion: str) -> str:
        """获取情感对应的表情符号"""
        emoji_map = {
            'happy': '😊',
            'sad': '😢',
            'angry': '😠',
            'anxious': '😰',
            'lonely': '😔',
            'tired': '😴',
            'confused': '😕',
            'neutral': '😐'
        }
        return emoji_map.get(emotion, '😐')
    
    def show_healing_progress(self, progress: float):
        """显示疗愈进度"""
        progress_percent = int(progress * 100)
        
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem;">
            <h4>疗愈进度</h4>
            <div style="
                width: 100%;
                height: 20px;
                background: #f0f0f0;
                border-radius: 10px;
                overflow: hidden;
                margin: 1rem 0;
            ">
                <div style="
                    width: {progress_percent}%;
                    height: 100%;
                    background: linear-gradient(90deg, #4CAF50, #45A049);
                    border-radius: 10px;
                    transition: width 2s ease-in-out;
                    animation: progress-glow 2s ease-in-out infinite alternate;
                "></div>
            </div>
            <p>{progress_percent}% 完成</p>
        </div>
        
        <style>
        @keyframes progress-glow {{
            from {{ box-shadow: 0 0 5px #4CAF50; }}
            to {{ box-shadow: 0 0 15px #4CAF50; }}
        }}
        </style>
        """, unsafe_allow_html=True)
