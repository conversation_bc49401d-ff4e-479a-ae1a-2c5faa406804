"""
测试记忆存储模块
"""

from midnight_echo.memory_vault import MemoryVault
from midnight_echo.emotion_analyzer import <PERSON><PERSON><PERSON><PERSON>y<PERSON>

def test_memory_vault():
    vault = MemoryVault()
    analyzer = EmotionAnalyzer()
    
    # 测试存储记忆
    test_memories = [
        "今天我很开心，因为完成了一个重要的项目",
        "我感到很沮丧，工作上遇到了困难",
        "有点焦虑，不知道明天的面试会怎样"
    ]
    
    memory_ids = []
    for memory_text in test_memories:
        emotion_analysis = analyzer.analyze_emotion(memory_text)
        memory_id = vault.store_memory(memory_text, emotion_analysis, action="store")
        memory_ids.append(memory_id)
        print(f"存储记忆: {memory_text[:20]}... ID: {memory_id}")
    
    # 测试获取记忆
    print(f"\n总记忆数量: {len(vault.memories)}")
    
    # 测试根据情感获取记忆
    sad_memories = vault.get_memories_by_emotion("sad")
    print(f"悲伤记忆数量: {len(sad_memories)}")
    
    # 测试统计
    stats = vault.get_emotion_statistics()
    print(f"\n情感统计: {stats['emotions']}")
    
    # 测试疗愈洞察
    insights = vault.get_healing_insights()
    print(f"\n疗愈洞察: {insights}")

if __name__ == "__main__":
    test_memory_vault()
