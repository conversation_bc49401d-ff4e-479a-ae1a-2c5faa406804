"""
测试情感分析模块
"""

from midnight_echo.emotion_analyzer import EmotionA<PERSON>yzer

def test_emotion_analyzer():
    analyzer = EmotionAnalyzer()
    
    # 测试不同的文本
    test_texts = [
        "我今天很开心，阳光很好",
        "我感到很难过，什么都不想做",
        "我很愤怒，这太不公平了",
        "我有点焦虑，不知道该怎么办",
        "今天天气不错"
    ]
    
    for text in test_texts:
        result = analyzer.analyze_emotion(text)
        print(f"\n文本: {text}")
        print(f"情感: {result['sentiment']}")
        print(f"主导情感: {result['dominant_emotion']}")
        print(f"情感分数: {result['emotion_score']:.2f}")
        print(f"关键词: {result['keywords']}")

if __name__ == "__main__":
    test_emotion_analyzer()
