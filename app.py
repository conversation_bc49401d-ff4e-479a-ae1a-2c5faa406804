"""
午夜回响 - 主应用界面
Midnight Echo - Main Application Interface
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd
import time
import json

from midnight_echo.emotion_analyzer import EmotionAnalyzer
from midnight_echo.memory_vault import MemoryVault
from midnight_echo.healing_generator import <PERSON><PERSON><PERSON>enerator
from midnight_echo.music_recommender import <PERSON>Recommender
from midnight_echo.visual_effects import VisualEffects

# 页面配置
st.set_page_config(
    page_title="午夜回响 | Midnight Echo",
    page_icon="🌙",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #4A90E2;
        font-size: 2.5rem;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .emotion-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .healing-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .memory-action-btn {
        background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
        border: none;
        border-radius: 25px;
        color: white;
        padding: 10px 20px;
        margin: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .memory-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 初始化组件
@st.cache_resource
def init_components():
    """初始化应用组件"""
    analyzer = EmotionAnalyzer()
    vault = MemoryVault()
    generator = HealingGenerator()
    music_recommender = MusicRecommender()
    visual_effects = VisualEffects()
    return analyzer, vault, generator, music_recommender, visual_effects

def main():
    """主应用函数"""
    # 初始化组件
    analyzer, vault, generator, music_recommender, visual_effects = init_components()
    
    # 主标题
    st.markdown('<h1 class="main-header">🌙 午夜回响 Midnight Echo</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; color: #666; font-size: 1.2rem;">情感树洞与疗愈路径生成器</p>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 🎯 导航菜单")
        
        # 菜单选项
        menu_options = {
            "🌸 情感树洞": "vault",
            "💝 记忆封存": "memory",
            "🌟 疗愈路径": "healing",
            "📊 情感统计": "stats",
            "🔍 记忆搜索": "search"
        }
        
        selected_page = st.selectbox("选择功能", list(menu_options.keys()))
        page_key = menu_options[selected_page]
        
        # 显示统计信息
        st.markdown("---")
        st.markdown("### 📈 快速统计")
        stats = vault.get_emotion_statistics()
        st.metric("总记忆数", stats['total_memories'])
        
        if stats['emotions']:
            dominant_emotion = max(stats['emotions'], key=stats['emotions'].get)
            st.metric("主导情感", dominant_emotion)
    
    # 主内容区域
    if page_key == "vault":
        show_emotion_vault(analyzer, vault, generator, visual_effects)
    elif page_key == "memory":
        show_memory_archive(vault)
    elif page_key == "healing":
        show_healing_paths(generator, analyzer, music_recommender)
    elif page_key == "stats":
        show_emotion_statistics(vault)
    elif page_key == "search":
        show_memory_search(vault)

def show_emotion_vault(analyzer, vault, generator, visual_effects):
    """显示情感树洞页面"""
    st.markdown("## 🌸 情感树洞")
    st.markdown("在这里，你可以安全地倾诉内心的想法和感受...")
    
    # 文本输入区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        user_input = st.text_area(
            "💭 写下你的想法...",
            height=200,
            placeholder="在这里倾诉你的内心世界，无论是快乐、悲伤、愤怒还是困惑...",
            help="这是一个完全私密的空间，你的想法只有你能看到"
        )
        
        if st.button("🔮 分析情感", type="primary", use_container_width=True):
            if user_input.strip():
                with st.spinner("正在分析你的情感..."):
                    # 情感分析
                    emotion_result = analyzer.analyze_emotion(user_input)
                    
                    # 存储到session state
                    st.session_state.current_emotion = emotion_result
                    st.session_state.current_text = user_input
                    
                    # 显示分析结果
                    show_emotion_analysis(emotion_result, visual_effects)

                    # 生成疗愈路径
                    healing_path = generator.generate_healing_path(emotion_result)
                    st.session_state.current_healing = healing_path

                    # 显示疗愈建议
                    show_healing_suggestion(healing_path)
            else:
                st.warning("请先输入一些文字")
    
    with col2:
        st.markdown("### 🎭 情感指南")
        st.info("""
        **如何使用情感树洞：**
        
        1. 📝 写下你的真实感受
        2. 🔍 点击分析情感
        3. 💡 查看疗愈建议
        4. 🗂️ 选择记忆处理方式
        5. 🌱 开始疗愈之旅
        """)
        
        # 记忆处理选项
        if 'current_emotion' in st.session_state:
            st.markdown("### 🗂️ 记忆处理")
            
            action_col1, action_col2 = st.columns(2)
            
            with action_col1:
                if st.button("📦 封存记忆", help="将这段记忆安全封存"):
                    store_memory("seal")
                
                if st.button("🎈 放飞记忆", help="象征性地释放这段记忆"):
                    store_memory("release")
            
            with action_col2:
                if st.button("💾 保存记忆", help="保存这段记忆以备回顾"):
                    store_memory("store")
                
                if st.button("🗑️ 粉碎记忆", help="彻底删除这段记忆"):
                    store_memory("destroy")

def store_memory(action):
    """存储记忆"""
    if 'current_emotion' in st.session_state and 'current_text' in st.session_state:
        vault = MemoryVault()
        memory_id = vault.store_memory(
            st.session_state.current_text,
            st.session_state.current_emotion,
            action=action
        )
        
        # 显示动画效果
        if action == "seal":
            st.success("📦 记忆已被安全封存，它将在你的内心深处静静沉睡...")
            st.balloons()
        elif action == "release":
            st.success("🎈 记忆如孔明灯般飞向夜空，带走了你的烦恼...")
            st.balloons()
        elif action == "store":
            st.success("💾 记忆已保存，你可以随时回顾这段经历...")
        elif action == "destroy":
            st.success("🗑️ 记忆已被粉碎，就像从未存在过一样...")
            st.snow()
        
        # 清除session state
        if 'current_emotion' in st.session_state:
            del st.session_state.current_emotion
        if 'current_text' in st.session_state:
            del st.session_state.current_text
        if 'current_healing' in st.session_state:
            del st.session_state.current_healing

def show_emotion_analysis(emotion_result):
    """显示情感分析结果"""
    st.markdown("### 🎭 情感分析结果")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        sentiment_color = {
            'positive': '#4CAF50',
            'negative': '#F44336',
            'neutral': '#FF9800'
        }
        color = sentiment_color.get(emotion_result['sentiment'], '#9E9E9E')
        
        st.markdown(f"""
        <div class="emotion-card" style="background: {color};">
            <h4>情感倾向</h4>
            <h2>{emotion_result['sentiment'].upper()}</h2>
            <p>强度: {abs(emotion_result['emotion_score']):.2f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        emotion_emoji = {
            'happy': '😊', 'sad': '😢', 'angry': '😠',
            'anxious': '😰', 'lonely': '😔', 'tired': '😴',
            'confused': '😕', 'neutral': '😐'
        }
        emoji = emotion_emoji.get(emotion_result['dominant_emotion'], '😐')
        
        st.markdown(f"""
        <div class="emotion-card">
            <h4>主导情感</h4>
            <h2>{emoji} {emotion_result['dominant_emotion']}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        keywords_str = ', '.join(emotion_result['keywords'][:5]) if emotion_result['keywords'] else '无'
        st.markdown(f"""
        <div class="emotion-card">
            <h4>关键词</h4>
            <p>{keywords_str}</p>
        </div>
        """, unsafe_allow_html=True)

def show_healing_suggestion(healing_path):
    """显示疗愈建议"""
    st.markdown("### 🌟 个性化疗愈路径")
    
    # 疗愈卡片
    music = healing_path['music']
    st.markdown(f"""
    <div class="healing-card">
        <h3>🎵 推荐音乐</h3>
        <h4>{music['title']}</h4>
        <p>艺术家: {music['artist']} | 类型: {music.get('genre', music.get('type', '未知'))}</p>
        <p>心境: {music['mood']} | 时长: {music.get('duration', '未知')}</p>
        <p>{music.get('description', '')}</p>
    </div>
    """, unsafe_allow_html=True)

    # 音乐链接
    if 'links' in music:
        st.markdown("#### 🎧 在线收听")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown(f"[🔴 YouTube]({music['links']['youtube']})")
        with col2:
            st.markdown(f"[🟢 Spotify]({music['links']['spotify']})")
        with col3:
            st.markdown(f"[🍎 Apple Music]({music['links']['apple_music']})")
        with col4:
            st.markdown(f"[🎵 网易云音乐]({music['links']['netease']})")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🧘 正念冥想")
        st.info(healing_path['meditation'])
        
        st.markdown("#### 🎯 建议活动")
        st.success(healing_path['activity'])
    
    with col2:
        st.markdown("#### 💫 励志语句")
        st.warning(healing_path['quote'])
        
        st.markdown("#### ⏱️ 疗愈信息")
        st.metric("预计时长", f"{healing_path['duration']}分钟")
        st.metric("疗愈类型", healing_path['type'])
    
    # 疗愈步骤
    with st.expander("📋 详细疗愈步骤"):
        for i, step in enumerate(healing_path['steps'], 1):
            st.write(f"{i}. {step}")

def show_memory_archive(vault):
    """显示记忆档案页面"""
    st.markdown("## 💝 记忆档案")
    
    memories = vault.get_recent_memories(20)
    
    if not memories:
        st.info("还没有任何记忆，去情感树洞写下你的第一个想法吧！")
        return
    
    # 按动作分类显示
    actions = {
        'store': '💾 保存的记忆',
        'seal': '📦 封存的记忆', 
        'release': '🎈 放飞的记忆',
        'destroy': '🗑️ 已粉碎的记忆'
    }
    
    for action, title in actions.items():
        action_memories = [m for m in memories if m['action'] == action]
        if action_memories:
            st.markdown(f"### {title}")
            
            for memory in action_memories[:5]:  # 只显示前5个
                with st.expander(f"{memory['timestamp'][:10]} - {memory['emotion_analysis']['dominant_emotion']}"):
                    st.write(memory['content'])
                    st.caption(f"情感: {memory['emotion_analysis']['sentiment']} | 分数: {memory['emotion_analysis']['emotion_score']:.2f}")

def show_healing_paths(generator, analyzer):
    """显示疗愈路径页面"""
    st.markdown("## 🌟 疗愈路径生成器")
    
    st.info("输入你当前的感受，获取个性化的疗愈建议")
    
    quick_emotions = st.selectbox(
        "或选择快速情感模板：",
        ["自定义输入", "我感到很沮丧", "我很焦虑", "我感到孤独", "我很愤怒", "我很疲惫"]
    )
    
    if quick_emotions != "自定义输入":
        emotion_result = analyzer.analyze_emotion(quick_emotions)
        healing_path = generator.generate_healing_path(emotion_result)
        
        st.markdown("### 🎭 情感分析")
        show_emotion_analysis(emotion_result)
        
        st.markdown("### 🌟 疗愈建议")
        show_healing_suggestion(healing_path)

def show_emotion_statistics(vault):
    """显示情感统计页面"""
    st.markdown("## 📊 情感统计分析")
    
    stats = vault.get_emotion_statistics()
    
    if stats['total_memories'] == 0:
        st.info("还没有足够的数据进行统计分析")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 情感分布饼图
        if stats['emotions']:
            fig_pie = px.pie(
                values=list(stats['emotions'].values()),
                names=list(stats['emotions'].keys()),
                title="情感分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
    
    with col2:
        # 动作分布
        if stats['actions']:
            fig_bar = px.bar(
                x=list(stats['actions'].keys()),
                y=list(stats['actions'].values()),
                title="记忆处理方式分布"
            )
            st.plotly_chart(fig_bar, use_container_width=True)
    
    # 疗愈洞察
    insights = vault.get_healing_insights()
    st.markdown("### 🔍 疗愈洞察")
    st.json(insights)

def show_memory_search(vault):
    """显示记忆搜索页面"""
    st.markdown("## 🔍 记忆搜索")
    
    search_query = st.text_input("搜索记忆内容：", placeholder="输入关键词搜索你的记忆...")
    
    if search_query:
        results = vault.search_memories(search_query)
        
        if results:
            st.success(f"找到 {len(results)} 条相关记忆")
            
            for memory in results:
                with st.expander(f"{memory['timestamp'][:16]} - {memory['emotion_analysis']['dominant_emotion']}"):
                    st.write(memory['content'])
                    st.caption(f"动作: {memory['action']} | 情感: {memory['emotion_analysis']['sentiment']}")
        else:
            st.warning("没有找到相关记忆")

if __name__ == "__main__":
    main()
