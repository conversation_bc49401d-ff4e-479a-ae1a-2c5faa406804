pyarrow-20.0.0.dist-info/DELVEWHEEL,sha256=1j1M2K__vV09ZGcLgptdNhtRgyVL7lt7CA5PSTOILxQ,239
pyarrow-20.0.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
pyarrow-20.0.0.dist-info/LICENSE.txt,sha256=Sy7K9umfPRayNg5Yj_WP85MuOvGz4fGlu8ACUdn0zcM,114065
pyarrow-20.0.0.dist-info/METADATA,sha256=CJOLdbIuwuOq8gW3YZDwWSyep16NwWScZsmuokRuUa0,3373
pyarrow-20.0.0.dist-info/NOTICE.txt,sha256=07TWhvlB92gfMko8Yd0DwYLQfaGM6aSnyPQTgNGLFf0,3116
pyarrow-20.0.0.dist-info/RECORD,,
pyarrow-20.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow-20.0.0.dist-info/WHEEL,sha256=S3ttYz1BI4IlvADpUfT9uuCx7v03wFM4fUIn-_-A3XM,101
pyarrow-20.0.0.dist-info/top_level.txt,sha256=Zuk_c1WeinXdMz20fXlEtGC67zfKOWuwU8adpEEU_nI,18
pyarrow.libs/msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll,sha256=pUKfCOyeSxLIn_tGHY2YhJYBC3WFD3OYx02QjzvH1ms,576128
pyarrow/__init__.pxd,sha256=eC5b2a7fm-SmomDxOM02JS_5jrwnBTFc1tzxA7nLrYM,2237
pyarrow/__init__.py,sha256=stHjaMmQffQ8tqPcjRiCseaUgvKX75dqAdT2sHMfiws,19208
pyarrow/_acero.cp312-win_amd64.pyd,sha256=TQY5evlQKbg4V9yCUxtfYSOfedMgdxPoBHo6WTHFLww,198656
pyarrow/_acero.pxd,sha256=rjCRAq1oo6OyT0c1ISd3_RYOLmo99UEhk88J5Efn730,1484
pyarrow/_acero.pyx,sha256=BjwZvof-Js6IFfast1qAcj7pfmP8uMNWiG5eGnvmQOs,21740
pyarrow/_azurefs.pyx,sha256=l_ZnhfsRvoXLmyk3KpDHnU7wYxe13Dcir6Pdxuc3SNY,6720
pyarrow/_compute.cp312-win_amd64.pyd,sha256=k624HHuijluyW1lA44PN1kfQKRVw7JAwtD_sMaxquYs,871424
pyarrow/_compute.pxd,sha256=GQUnCeC9vy9YEnT3gHKBoQMrJvjjmjdCQuIYp6fXXio,2094
pyarrow/_compute.pyx,sha256=SY8NN2wvfgyJUTNZR3wMzKquO_gcW2GFmj5U4zuekBk,115015
pyarrow/_compute_docstrings.py,sha256=2KRhcMXk12GS_zihOVWsIjyU9tgPBpPNHUi-GpQPZ6I,1763
pyarrow/_csv.cp312-win_amd64.pyd,sha256=Es3E2iIOTk7RfWMy4KncdcJR1A4PosaTw4u2Ub4vm-M,264704
pyarrow/_csv.pxd,sha256=rI6eCBX4P1-C6wwhRySHK3vWvjy2dorVeAPaSdROeZQ,1693
pyarrow/_csv.pyx,sha256=gjmYIww-b33RQ9lrSG1Nn4lwgr7uTFy2sv_FzSrGtZU,56253
pyarrow/_cuda.pxd,sha256=S9wJXbxxM8MO-oAgWHkblKMdQr9zuGoNfKGkIKVXw0c,1989
pyarrow/_cuda.pyx,sha256=WnZRNpNvoOolvv767QxesGh5e0z-N_e0TEbkYeQtEco,36216
pyarrow/_dataset.cp312-win_amd64.pyd,sha256=IY0kgMmZkJJDOWDV7rWIUPR7OOH7_JSFD55qJ8Qr0gE,720896
pyarrow/_dataset.pxd,sha256=yakY7xxTWXmy6DCnXqxp2BH2X5_LzFb1Aj8p4eRJHEM,5127
pyarrow/_dataset.pyx,sha256=GTKzIr5-YgwiaWay72_KdEwplXyi5E6WrGgJcdcDeBw,166929
pyarrow/_dataset_orc.cp312-win_amd64.pyd,sha256=sZXOFSiaouj7zXrzhP4TVLh14Mal-GFTXfH-xBqt8UQ,63488
pyarrow/_dataset_orc.pyx,sha256=vgB9AQ_DjKvXddUNoksC2HqtlzLNmi-dqTEncd6DrCQ,1550
pyarrow/_dataset_parquet.cp312-win_amd64.pyd,sha256=kB77tNoNJE1vziIJu_oyxVq5cB1l_3V10vPkVxfXr-Y,257024
pyarrow/_dataset_parquet.pxd,sha256=ayCJyRcMOIVVKgtCYF2SgdGKHrTi-jfCrgJcDJMW4Mo,1615
pyarrow/_dataset_parquet.pyx,sha256=2L6YwWjw7qprxYZorP2J-ke6RUe_MN3rEthJrj2PajM,39933
pyarrow/_dataset_parquet_encryption.cp312-win_amd64.pyd,sha256=g_3cO3FS_eorjDT8FqCj3rRNQWqF7zM533tGsFpntqM,87040
pyarrow/_dataset_parquet_encryption.pyx,sha256=q5FVop8IERFxVChOUnHxplGjdVs9nR2o-S47dUARWEU,7407
pyarrow/_dlpack.pxi,sha256=wMZLifeA03bfnmKcbMfkkkBlCi2hhdLo2q6kg0IL6-0,1878
pyarrow/_feather.cp312-win_amd64.pyd,sha256=ZIBbf_DR7siu7HqAD7m59f9rGIqV7cW4xK0bD8REXNI,86016
pyarrow/_feather.pyx,sha256=uMhjMFDUvcfTVBaPCoydbmcRtbzP7Rz6C3v7T9bahGc,3890
pyarrow/_flight.cp312-win_amd64.pyd,sha256=TGIXMYMa0d1du8BzZaQU-pX2ePhLHjV9hVJpMX9mmGs,870912
pyarrow/_flight.pyx,sha256=DouzEeXhDXRZw3Z9yEw3mJ_pOPcGY6mUSLp1_rgJZZw,117402
pyarrow/_fs.cp312-win_amd64.pyd,sha256=72Yf4F0KJmRdGOUhQDdpZtb0sam8DCZnSegqzpOyaGo,360960
pyarrow/_fs.pxd,sha256=GckaT7VHRMB0si-WFHIfpLAcs5XjOLbbD1zSoI3KwdQ,2530
pyarrow/_fs.pyx,sha256=wjxQqy5omKvIXGsXwqG_MkArPRZO5MpP0RXZ_yPKdo8,54031
pyarrow/_gcsfs.cp312-win_amd64.pyd,sha256=kbRQ1-A6IRrRtR6U3Xm0FzQdKRDJ2zSTicXTonX6q-Q,90112
pyarrow/_gcsfs.pyx,sha256=AcIEmYc0KRuyjMWFwzNN-iFcanzBvtwv2zVJnZRoM_I,9255
pyarrow/_generated_version.py,sha256=K0RvSd4F4xReHIY4WdWt6kQnd7uLgd87aIk4yQ4dLoM,534
pyarrow/_hdfs.cp312-win_amd64.pyd,sha256=t4lMCL0_YeNGfxmH9KTvqWW7_xxXv10DiolnX277H10,89088
pyarrow/_hdfs.pyx,sha256=U8nrqeTLNAxFOOO5YCuO8c4otCrNe6b1N09NZG5OD7A,5967
pyarrow/_json.cp312-win_amd64.pyd,sha256=wjLjxdOquzalVGzpfw7y4SPDwO5NWLV80wwhea-YFvc,97280
pyarrow/_json.pxd,sha256=jAfiARHEM1qJ2mAuLPa_KrKAbU5YNFLWD5Rhj4P5c1U,1242
pyarrow/_json.pyx,sha256=IVMHB1d30fBI5gUhfFfJSvsVBetU4pn0erRb-zsgJiw,12925
pyarrow/_orc.cp312-win_amd64.pyd,sha256=SeEZ50KqTE1bMCmXPsKp9HEfPZ7mqI5uEB6IxEm9SDY,151040
pyarrow/_orc.pxd,sha256=G5jSZUTqyt_stuKn0y56L9lyG8oRqf8NzMtzYSD1qaw,5823
pyarrow/_orc.pyx,sha256=ZfgbW4OQnYshV8nQQCXculGpbmLGsKwzwRwNY59QWq8,16001
pyarrow/_parquet.cp312-win_amd64.pyd,sha256=RFFxQS8KMFq7VfaGqSl2KaPmYFKqfnwlXwdBEbYCdS4,412160
pyarrow/_parquet.pxd,sha256=rNgxYKYyhQetxB2wdEjLy301RImGveyYZoxxmk5KZB0,27699
pyarrow/_parquet.pyx,sha256=sgpqL_njq8xOS-QaHXdNCBUhTITrZN_Tkheyl8Vge1I,76093
pyarrow/_parquet_encryption.cp312-win_amd64.pyd,sha256=8T-meFxA2hhMDygPTDGML8gCAzYYgNkNe3i3SNmn2S0,176640
pyarrow/_parquet_encryption.pxd,sha256=WnrnVuKMmf7MhxDESBulUuWztko4HeNdadkOve_pCLQ,2642
pyarrow/_parquet_encryption.pyx,sha256=jfiJqwb1X3Ky7QSRm98OzGYOSkviwPaQzPEazjEJCIA,19072
pyarrow/_pyarrow_cpp_tests.cp312-win_amd64.pyd,sha256=IBudpaAyuch9ffSIhJZeBbK_XRpyXxZsz0sqOzTXX78,68608
pyarrow/_pyarrow_cpp_tests.pxd,sha256=L_cgXkO1M54NUhRpzHPqLA3-h5qkqTjcxt_OfSlF01I,1232
pyarrow/_pyarrow_cpp_tests.pyx,sha256=cXM3tGxJyLqr-U2oqTKzdWVEsr7d7FLbrSzjw6Aagl0,1815
pyarrow/_s3fs.cp312-win_amd64.pyd,sha256=lZyvCsLF1hwTWBFFRWFWJTcMq9HX23M1FjaK6m0bL_k,161280
pyarrow/_s3fs.pyx,sha256=CwmOQwxzOc9r8xk6Z4faLTM-zKzf4uAZh2LepC8H4bE,20107
pyarrow/_substrait.cp312-win_amd64.pyd,sha256=x1wchgNxL7i4Ifhv5rxPYYrVxql93rvaVkwggg3xON0,146432
pyarrow/_substrait.pyx,sha256=tOdpG8sK4oKgct6ymgQeu9bj_DD2Gb3vFRXrvSg_qOs,16147
pyarrow/acero.py,sha256=ymgF1ISBe8rlYRINDGK_do-xb7D_QwZ3ruAKNw2NsxY,15707
pyarrow/array.pxi,sha256=6y7M81KkSXYznChyPk5IglrBlZILIHjxgc9yn8JvpBA,161776
pyarrow/arrow.dll,sha256=D6C4SPvMZ-9HY2ts9NIPLtOcjNr3C269dl5l5Cze2ss,31191552
pyarrow/arrow.lib,sha256=Ca34cguMRW-tEWIWYaEXRSgezm0Yyu3hFU8o1RZvgbA,4200316
pyarrow/arrow_acero.dll,sha256=LWFwuTgmhBHvDLk_-PIdgi01ugGiUD_3a1nRE077UKI,1267712
pyarrow/arrow_acero.lib,sha256=wb_MXW2cPATnMgC_gW6ov-OFJQjJBhBV8g8ydZWx-zw,287988
pyarrow/arrow_dataset.dll,sha256=3lFzW9A90u-0v2nTX6vTAHHr2IoEqNzvcWaoP_v_Xww,1320960
pyarrow/arrow_dataset.lib,sha256=TlgEULy-BgbggSj8ey6vgNxlFEvEEBZvWbwwqh69a1U,413512
pyarrow/arrow_flight.dll,sha256=rPdII8yB8GmK45AkrHtTu-y2Phje1ANkKUWTpCjtz68,10710016
pyarrow/arrow_flight.lib,sha256=2iLfif49uFGiUYd7j_TF5G4LKAgWyNQmXKjwEzBY7qo,501002
pyarrow/arrow_python.dll,sha256=lGbowogIQmnZEFfSGBgGPJg2_elo3ScNGGicHEmWLv8,1212928
pyarrow/arrow_python.lib,sha256=HumIeZ3Sl9-yKIqyJAMkGvBcsE2UkoEMUUWm5VYMMWY,167464
pyarrow/arrow_python_flight.dll,sha256=sGa7A2uaX0h-5j2z1oPNOrRcQpLMmiausVeVOd2Qs14,62976
pyarrow/arrow_python_flight.lib,sha256=GOHpwdJ4mFbjJ1eS1Wr4npRzbsBEFqEgkepvRkYK0V0,58204
pyarrow/arrow_python_parquet_encryption.dll,sha256=SDmBV_MKabKilKtHnto6w2ynqnXgT2ZAhit5NoiyY1U,32256
pyarrow/arrow_python_parquet_encryption.lib,sha256=fROY3ZCzOx-YFfc8rI5qaLXrAjymDTyk1FEVwWrzBvQ,18398
pyarrow/arrow_substrait.dll,sha256=EMjv0wtD_UmWelGmXvS0eq1FxDCFbBa6rBjpD46gIEc,2370048
pyarrow/arrow_substrait.lib,sha256=mUaJYvyHprd_0q0WGL-Y-9RTqZ91TOMVMrHUIwrCfSw,111500
pyarrow/benchmark.pxi,sha256=mrrnfpGQcc4Dxw7EOo7vqzxW8NBcQ3DmPIAvp76U4aM,889
pyarrow/benchmark.py,sha256=i-yvjV5wmnxC-IMo1xAjys6mmM2ea1yyys6m7FzJt6g,877
pyarrow/builder.pxi,sha256=ZXVjiDsbO_Vw_25kUN43AuE0xj7Chw2Ypkrv45eNj2Y,4784
pyarrow/cffi.py,sha256=xRVBRPsxbd8yd5jE4dk4r_oCUonAei8tLBkUbKP-cCk,2477
pyarrow/compat.pxi,sha256=GGSfRvXAMyzcIu6jr6Gt_a7-a0kied3Sn9BwMY8quGY,1991
pyarrow/compute.py,sha256=fDO-qQEvc0UwYOnEswx0-qy2QvU-8Qx-48R83WZ9KDM,24999
pyarrow/config.pxi,sha256=XrfYecOH_SEfZ6GfKF12G6xcpzZ5DGoDplyRF-8G2J8,3187
pyarrow/conftest.py,sha256=INVCySmsd9WaoHjWX4wVnbmrTBpprO2Kx9pPBhqjWgQ,10285
pyarrow/csv.py,sha256=YdN28bUXVQVe03NbWzJxT6P_mf43pv0pbv463MJQ-Wc,996
pyarrow/cuda.py,sha256=NJdOLaIiUFOnTIeRCaxNttBJDqwe-hbSdww5ls5xSJM,1112
pyarrow/dataset.py,sha256=9TvB23FnqiCd1ShM8omG6tRNm-ivRDiqeIXqVPJxTqU,41342
pyarrow/device.pxi,sha256=0ny0N73xxTSDEb4mNbsBWQQVcyLZfILVZipPGJc_2fE,5737
pyarrow/error.pxi,sha256=Bl9lR06XhW7mbCz99INvc4Hrc14jlXsGbE4foHPmpqU,9183
pyarrow/feather.py,sha256=MpUtZP2SGLWNf7UUuWOt9iSta3QJhIzFdK-xKxYnfzc,10236
pyarrow/flight.py,sha256=MLoZiSHzbRBjS7CkR_Na-D8LIBBUR9sOQyx8fDr41zg,2246
pyarrow/fs.py,sha256=N8-xcNWCyB2WjdLEYlShniaqdLZGHpkW_GJiIi-0xq8,15330
pyarrow/gandiva.pyx,sha256=5eyKAfCNoBqqvldXDsqqRE_CT3niqreSicAxiWify1g,25263
pyarrow/include/arrow/acero/accumulation_queue.h,sha256=GGQKzB7AAWvOCn4bpcTZYhTcjRSZbQ5jxN5Zx_bEtXU,6147
pyarrow/include/arrow/acero/aggregate_node.h,sha256=rVzdpk8AthruUdmAffAOUNwbCk-TkIp5ccTP1OheTu8,2259
pyarrow/include/arrow/acero/api.h,sha256=ja6ScTS8T_skQGjBaYvgWPX1MBq1brRlYfghWIBsTTk,1183
pyarrow/include/arrow/acero/asof_join_node.h,sha256=hxPcEiCNC1Ssch8dWu3OmpAmOCMCjWd3wp8VGd7qsoc,1531
pyarrow/include/arrow/acero/backpressure_handler.h,sha256=sv-RN2oUGieDG6c9CP_cMVFcq_SYZae7Cce7ksGeQ10,2884
pyarrow/include/arrow/acero/benchmark_util.h,sha256=UE4Xchd9Euwsj5IWtNGYPRzUk4__ld_46yQdaKy8jFw,1991
pyarrow/include/arrow/acero/bloom_filter.h,sha256=AI0q2YmV9rzvRSGVbCQ5P5ZGMBA-XSrRGeTFCaKd840,12301
pyarrow/include/arrow/acero/exec_plan.h,sha256=icuTf4Vj8rbQvs5XcymSGiEkOsDkvoey_8Zs_HQG--Y,36728
pyarrow/include/arrow/acero/hash_join.h,sha256=1IF3MC8L9U3xtjCS2tCp5ZHANY8hYUcAcKBA02CiEdc,3097
pyarrow/include/arrow/acero/hash_join_dict.h,sha256=yO6tUuaMiNigMvIcecnaaCm1m0vO4RnF3AW3INGEvko,15678
pyarrow/include/arrow/acero/hash_join_node.h,sha256=dxmanwGxru6Ilk4dGouZ4EZy5G8MYpiR3P_Xi1rJorg,4481
pyarrow/include/arrow/acero/map_node.h,sha256=g7SnGKKpDrDxJAeLVRE2dYiu1ASWWfKAsXOovFURDRc,2709
pyarrow/include/arrow/acero/options.h,sha256=YV5y09sfUN-TQfcoVInhD35JClK086eji1cc5CbYZEQ,38263
pyarrow/include/arrow/acero/order_by_impl.h,sha256=Cel0AcoqvQxRHbVFU0nOoqN_omfw_BsD5sQVlo7m7fQ,1747
pyarrow/include/arrow/acero/partition_util.h,sha256=zHbxqFgBjCj5Qe7aVyGY27EzlqYDZvICNKveFow_l5I,7621
pyarrow/include/arrow/acero/pch.h,sha256=SCfsZcIE1gMn4HDG0mcisMTc6AY707D_MurvzJNYnKg,1117
pyarrow/include/arrow/acero/query_context.h,sha256=AK7weKm9V7oC5VMvMoAfD6QbYUH3Gwc4QYw5pext4PA,6363
pyarrow/include/arrow/acero/schema_util.h,sha256=krGNXK-XUVhlYvAsLARhGaT5_P3b6jaWt9DF3hiwtMk,8187
pyarrow/include/arrow/acero/task_util.h,sha256=PCgJOqWIHvFm4xEw0KhSb49syeIGlyC3npyFQGfzF7E,3808
pyarrow/include/arrow/acero/test_nodes.h,sha256=wjrgNEl6tIG207vxFVqIO_HbZ5gmwsViKS4az88BhuM,2963
pyarrow/include/arrow/acero/time_series_util.h,sha256=zRkyQY6_JoXV3715mHGxs2c9a9BM8rDTMbBLF6qcCGc,1241
pyarrow/include/arrow/acero/tpch_node.h,sha256=8RTrew_dsrCb5P0ltZ6CRInrjb9R8GqfH_xlI5e83Vc,2736
pyarrow/include/arrow/acero/type_fwd.h,sha256=ywhZdjTAngHGlVcg9SquUKtC-8pZbZaGPQ-vfnbTaXM,1139
pyarrow/include/arrow/acero/util.h,sha256=ZF5uRHwkNWg96zRvue9McAEoNJp_Kp7SMUAeX4oGKPI,6305
pyarrow/include/arrow/acero/visibility.h,sha256=AkfHK0MhXhWEHUuNn2E7LkfTSLVQ3OsQWEzZL67tB-g,1666
pyarrow/include/arrow/adapters/orc/adapter.h,sha256=B7QodNYkYUns8IZhLsygyLlcIDOm47HNRyqAqUf5Qac,11354
pyarrow/include/arrow/adapters/orc/options.h,sha256=vMxdAhBsxRoLUhi1KduGh0B_Hy9l-JmNZI4QQKNJ840,3816
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=PBuoNq4ASqWeVqi9cjrTTBc_MOn_FrFUH2uosRg8PIk,3593
pyarrow/include/arrow/api.h,sha256=HajNiML4B--LKCoUN1SX4V_J8nwoFtf7rWcI4YndkM8,2538
pyarrow/include/arrow/array.h,sha256=cAYOBSZOk_RffGZsl994n2Z1utf0U6Xr25K_UbQ24Y4,2030
pyarrow/include/arrow/array/array_base.h,sha256=PhCbm-6KWvv0YmSnGRDtReK8WQIOIdulgkQGwP7aa6E,12464
pyarrow/include/arrow/array/array_binary.h,sha256=cFO7LYPTXKxCBHxylwQJx35PSGzxGyMr8my6fvX8HrI,11568
pyarrow/include/arrow/array/array_decimal.h,sha256=1pr51z_v1LeUPynfDexkpFQfD5MfcONS9J99DJRPpQw,3209
pyarrow/include/arrow/array/array_dict.h,sha256=_rafDEnCRQsrbAA5TncNdF7qp0z0JOn0bHqaB_2yQwQ,7793
pyarrow/include/arrow/array/array_nested.h,sha256=IdCDq-0tn0SKsd1dARhjx5ex-XqT0SXCZDBdn09GR10,38492
pyarrow/include/arrow/array/array_primitive.h,sha256=tVo2Nkaw7Rgxn9-ZGL1DG582VSjoI6Qnm01FtPTD_6I,8404
pyarrow/include/arrow/array/array_run_end.h,sha256=9ZsbKbROro2Cj3e-QXJyjoDscjbDa15A3eJKXVxT_VY,5234
pyarrow/include/arrow/array/builder_adaptive.h,sha256=z6xhvrZdME7Uc3Yw092xhiSOnA2RGq_vJQXCRtNOq_0,7076
pyarrow/include/arrow/array/builder_base.h,sha256=RQahyZ-iZjrz_UsCu_Ub0ekIe-WHMZhZ6ZuqlCS_FYw,14094
pyarrow/include/arrow/array/builder_binary.h,sha256=eq0FQ32glPy1qRlM45uvmtdEwp_qWJjy3tucqBM556A,33689
pyarrow/include/arrow/array/builder_decimal.h,sha256=6NHMQaRvqtCSuYoeyVFkpWW1DMaWKkM3cBHePossbJQ,5215
pyarrow/include/arrow/array/builder_dict.h,sha256=nF4JDCER_0_7UaFr0fxe1zVFsGNFhtbJ4sPZ7xEEfiI,28627
pyarrow/include/arrow/array/builder_nested.h,sha256=FK7ZrhNMq482AshcJG9g7CQDjllI5yCW_jBiyn9YoUM,32067
pyarrow/include/arrow/array/builder_primitive.h,sha256=t1GDGj6S-36xgvgEAdv7SOmczNp1wizCiXoMhvqgYtA,21488
pyarrow/include/arrow/array/builder_run_end.h,sha256=XLX6GrEjnYcVul_Tp_c3NGProE0OpogU-xeI55nhtlk,11719
pyarrow/include/arrow/array/builder_time.h,sha256=L7-Kb-5xOUYkBp98Y7vBMCPyQba5qXb5oChSgMtvaSM,2614
pyarrow/include/arrow/array/builder_union.h,sha256=QXRVqMhp3oHs-qU6xl0y1-KjrYq2LdG2XbbzGN3tQMM,10398
pyarrow/include/arrow/array/concatenate.h,sha256=JhkBRuB5TM6KbU4NQ_flPeVHDTR5Kg0C77XBz3uB9qM,2112
pyarrow/include/arrow/array/data.h,sha256=7C3poZr5IXv4obJPo9N_D1dIUGFXXKgaRvD9mnPBVO8,25834
pyarrow/include/arrow/array/diff.h,sha256=vGHW50FpxoMfYMVRvzHcxx3tw-02letyoXkR5yoxgr4,3420
pyarrow/include/arrow/array/statistics.h,sha256=g-7bGMtr4lmQFQNYzbBnI-LD3slzTWU-zw4cOQov33k,5467
pyarrow/include/arrow/array/util.h,sha256=T48nkLcn4eUkvFWe40eIIBG4ZiB221snra0gACpEIjo,3748
pyarrow/include/arrow/array/validate.h,sha256=9O3KehNl2kWHQwLz6A7izxqW2McmpVcmOE2nz1glSBY,1766
pyarrow/include/arrow/buffer.h,sha256=yU-bdhk-kaYX492IFKQwelV1TMBBsFus9Gx_O54Tjkw,23808
pyarrow/include/arrow/buffer_builder.h,sha256=N678fzVzUCBXJL-vLIYmwECm-0Yx0crNmuaEhzHZJLM,17855
pyarrow/include/arrow/builder.h,sha256=MNcxVdcYID5LCq_U59nwuObUVshQiEB3_5ij65s8JVI,1579
pyarrow/include/arrow/c/abi.h,sha256=WzE98mONnJJzA9pTj7g9rrdjMNBOznmRf1arZJYuHHw,20778
pyarrow/include/arrow/c/bridge.h,sha256=F0wa9Z9e3-gMdiwvDftaxYT2ao_W3gLGUT623x8RT8E,22278
pyarrow/include/arrow/c/dlpack.h,sha256=1pvKCU6HiHs0RFUYLbsU8iQu4ALce0aE1I9D3sqKzPE,1868
pyarrow/include/arrow/c/dlpack_abi.h,sha256=NIJjDKuCpgkL3_OoGaJQ4apP5kW0z38Fw2Gw56GR6Jg,10241
pyarrow/include/arrow/c/helpers.h,sha256=K87ufXnopM9P9KLlI2r70-E35F-RnaVXV_bzZWtT0-s,6457
pyarrow/include/arrow/chunk_resolver.h,sha256=9qIdJxsGq4kPALXnSEsZUN7Gx7h2v-drutsG81jnt9Y,13135
pyarrow/include/arrow/chunked_array.h,sha256=4R6KLJvU1WgaJFH2OtwSsJtkbY2TRjGxS75NfEG91nk,10930
pyarrow/include/arrow/compare.h,sha256=4c1ulUqzm4gqkh8Rz1i7Iydgg3_vZmxSvfiIg1pvj24,5700
pyarrow/include/arrow/compute/api.h,sha256=qcAMa_sJ1XIW_j6JA3HHQz8geNBDvWBtHwYNV1nQkBY,2124
pyarrow/include/arrow/compute/api_aggregate.h,sha256=JCI1OXK6Q3jGGSU7gkIUnjqxAH9RxewkhLl0TixNYHQ,22541
pyarrow/include/arrow/compute/api_scalar.h,sha256=mfZivcH2tRZ0x4mMKsYRQ8zZLj_JLJZcCNv7SdEEoNA,71516
pyarrow/include/arrow/compute/api_vector.h,sha256=h5strsT0d3dajRe-nmo9eje3Zinh4diGD1oNf3O0wqM,35342
pyarrow/include/arrow/compute/cast.h,sha256=aCqOeB_N7h2qGChv9yAi9NqUZ_33FptkCoIFCmOieog,4379
pyarrow/include/arrow/compute/exec.h,sha256=GzwemTPvEjJPGjW9GRpvMzUn8jhbWNs7FngwSSKPagI,18464
pyarrow/include/arrow/compute/expression.h,sha256=mL4N8rcRrz7XCm_x92BGo4cFMn3_j1ZR0CM1NworjzQ,11479
pyarrow/include/arrow/compute/function.h,sha256=L5oWje4X82sATTQrd2HL4Db1-gg6DwH8zOyw0odnYFM,16754
pyarrow/include/arrow/compute/function_options.h,sha256=FnCqSjeZG13AUwDx65pOj8i3ZbHPhzL4hcpyRgpB9Gw,3169
pyarrow/include/arrow/compute/kernel.h,sha256=ZUFUO0FLpVhMWbLgUpf9bU_OFVaJxyZF-TOS3HHHG3Y,32159
pyarrow/include/arrow/compute/ordering.h,sha256=ja1S3q-ywF2y02-5-jPcG1KVNbx_qLRJ5Q9NIr8mxco,4249
pyarrow/include/arrow/compute/registry.h,sha256=kZ4rf8OIM0F-0wlfO66KMIDctZpDdt9asfCDt-6tRcg,4963
pyarrow/include/arrow/compute/row/grouper.h,sha256=eOwhijKPbvTQIzb0NzOAlHs_gRHO-rDpPMhV87ryoSc,7655
pyarrow/include/arrow/compute/type_fwd.h,sha256=uvRR4uzdCp6KMhKrtK6SSlnIeuHakRUt1dgXC8ayzyY,1614
pyarrow/include/arrow/compute/util.h,sha256=E9PmSQHRmGiktCAh222Vi-1OjpUM3OXXM9C7kN0t0ro,9078
pyarrow/include/arrow/config.h,sha256=_-TrLaku5IaDU8KR1K3Y1gmLJKhHv6teKgZHIFC9nEk,3142
pyarrow/include/arrow/csv/api.h,sha256=mrySGIM81ziJhlG3AutB28biUs42PNvpERB63vFfXQM,929
pyarrow/include/arrow/csv/chunker.h,sha256=KYhQi-hLLmFIhC3BRfZRbIOpZMsTbnWHS08jy4tcCl8,1207
pyarrow/include/arrow/csv/column_builder.h,sha256=tvZJ71f-fylYtrEmkWwIxgb_s2WLiUnPwyBpBLCHdjU,2968
pyarrow/include/arrow/csv/column_decoder.h,sha256=1Mfwma0yKt5cYVQ-jb4kaHk6VzrnyrI5Pg397R-Mm3E,2422
pyarrow/include/arrow/csv/converter.h,sha256=eBnvOyrqWZ7bexpZc35jBFouaVFFn0NyaAM0P0chcfQ,2871
pyarrow/include/arrow/csv/invalid_row.h,sha256=QI1O1M00RItEnFeMYTvJCUOQ88n-GyO0IantTDu73T4,1944
pyarrow/include/arrow/csv/options.h,sha256=xmUJXmLQVePc7alEXt1vaWWVEU2HpdDOr1SyzvqrgAk,8200
pyarrow/include/arrow/csv/parser.h,sha256=rT4dfOfzKBFAbe5WQmrWtKbZ_DtFRGN4VaMcHhHVc_Y,8844
pyarrow/include/arrow/csv/reader.h,sha256=njP5lhKW0XptbU_Zz3LakWZdvrg7Eko2N9i0vKEKBcg,4718
pyarrow/include/arrow/csv/test_common.h,sha256=dw0MWN5occOCyrIoIOzppsWMY83Br2FMgTDGcWC-EFk,2027
pyarrow/include/arrow/csv/type_fwd.h,sha256=udWl775X1Yx5QBgfmXluDDmgI2NStrjmur91H8FNvg8,1012
pyarrow/include/arrow/csv/writer.h,sha256=9cBNYlJRIdqJ6RlWhjpbVq0pBPVwgIdkmc4up9Cah3s,3639
pyarrow/include/arrow/dataset/api.h,sha256=zUvEjz-PuLXDdqE_WUVIWAwHAmz77XwqI8eDBqpHAxQ,1361
pyarrow/include/arrow/dataset/dataset.h,sha256=D6fwrRvAofum40w65XwDqaLuChueQqI5ZPE_Sdq3Mo8,20818
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=eq88v7MOtVt44fg54_1bo1vzdz8v4RPJGuLVY_ai1UU,4692
pyarrow/include/arrow/dataset/discovery.h,sha256=W1SwsuLp-H9IzogC7Ew4_DbatXPH0AERyY0uzDVbtaY,11511
pyarrow/include/arrow/dataset/file_base.h,sha256=62StSlXAvcHfXU8bBbz_8v1RgkIorwiUsG_D9z091MQ,20698
pyarrow/include/arrow/dataset/file_csv.h,sha256=MCt6eRH9CKrfmMPmZUiQcJePXAT8ynI4qeLcnhV6pHg,5160
pyarrow/include/arrow/dataset/file_ipc.h,sha256=FaCnXdg2YihUDxcdBoCvSmA-MTk-VKhD-rN5T2WqEkU,4206
pyarrow/include/arrow/dataset/file_json.h,sha256=JH-9JGSg351WTLG6ET6YCQ30w-Kfcd1Uwa09nAQX0LA,3621
pyarrow/include/arrow/dataset/file_orc.h,sha256=i5In5ilw82h0wil4IbSJpFfSU239_6bFsZZAx3e4vQI,2527
pyarrow/include/arrow/dataset/file_parquet.h,sha256=bifvhCZpWRCkNRSplL-wDoUKLzaBBqRAbYRs6lV1Fys,17287
pyarrow/include/arrow/dataset/parquet_encryption_config.h,sha256=8wPZEYEs9-95TaL8hwZcsAmmzldvg20lqBFLT4YMwNA,3500
pyarrow/include/arrow/dataset/partition.h,sha256=P9uWiM2sCk8LWEl3vfzxBwDZHdKGtjehzALCctBq3As,17247
pyarrow/include/arrow/dataset/pch.h,sha256=3vmHGXlgxa9tBmj1KBA3bJxV1dy_Vp_32vQDeivp5iM,1221
pyarrow/include/arrow/dataset/plan.h,sha256=e3HxDV55B9606UzmQfJOchsZYvxNjjSRW1sSeqRdA38,1214
pyarrow/include/arrow/dataset/projector.h,sha256=Gl7qon2MGuQdAXFC_VL7lDmekOoaIYpkDbqvhHaOmtg,1167
pyarrow/include/arrow/dataset/scanner.h,sha256=BLMu-WiJQvpIH_Hn3sc8lWOngk223Jgn2Czo1nl5tlI,26532
pyarrow/include/arrow/dataset/type_fwd.h,sha256=3E2qO5bZAg-AAtFGZGGpWP_jidXMKyxgsZL7KhlquvE,3283
pyarrow/include/arrow/dataset/visibility.h,sha256=hNHTDNAve7L5iGW8cE-ZD8_FwF50tSOkPIXHgnQmWUA,1636
pyarrow/include/arrow/datum.h,sha256=qLmvRILWuUfJCWZ1b1_dXAN_NQRwebjjdbSs7ow2UPk,11825
pyarrow/include/arrow/device.h,sha256=M0wyVZkkbM7G2h4pISWygSJfxUStvsNljOjmQ2ZHIGg,15726
pyarrow/include/arrow/device_allocation_type_set.h,sha256=m3zFNvCKW61HQhaH65_97m-y73Y0mQTOTHTSJFMSpT0,3403
pyarrow/include/arrow/engine/api.h,sha256=fgaw48YHO1VT7cqs5htHz--cpk4Zh9nAsn23ySIMx4I,908
pyarrow/include/arrow/engine/pch.h,sha256=SCfsZcIE1gMn4HDG0mcisMTc6AY707D_MurvzJNYnKg,1117
pyarrow/include/arrow/engine/substrait/api.h,sha256=cet1Nuy9YHB_TC87wqBbljw95HcuOABH9cBumBtrhLY,1105
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=XK9PFvRhyYUCR5aiOjnpDimCDDLeQEd_PNnGLJXW9rs,22033
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=XuG6iwVFe8MtMGSOyAgL1HnPrHOS3O6Nvcf79Ew4zr8,3165
pyarrow/include/arrow/engine/substrait/options.h,sha256=pYixPsZpfJoc_2DK6ludyaAjZtGsWIh8D4VQ-UEnREY,5955
pyarrow/include/arrow/engine/substrait/relation.h,sha256=YwP9-cQSQXp6YbHN5tuO-KgiL1Pu1GRhUAAlV_9V2mo,2456
pyarrow/include/arrow/engine/substrait/serde.h,sha256=CDi5lFowebB2mlxr8JvfpgNFV2x1fiYiXtrEf5O4QSo,16859
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=rb4YPCPcFnzYKBX3q0Ar-ceTdtGwFpkOqCK3IsJn4e4,3079
pyarrow/include/arrow/engine/substrait/test_util.h,sha256=TGz1HkUVePC7x29UW1sqc2mP1F49n4kGVdTeLJab3gQ,1562
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=aUfGCwouyEBJget9lsYmu6H_Je2AmKG_w0R7n18breI,1060
pyarrow/include/arrow/engine/substrait/util.h,sha256=CacFeBx3Zb_tuS4lL1Oo6Mu2xbH9VpMxEWavuw2HQh8,3653
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=SjFehPWLcF8wifrR5gii-C1_yUr95k43uYONab6nYbs,1792
pyarrow/include/arrow/extension/bool8.h,sha256=kpVXrN0XbSZkz5ttuCdrQ9y7QZuZoSc1OqcaUOoTv90,2205
pyarrow/include/arrow/extension/fixed_shape_tensor.h,sha256=FY5tx6BATFM4w1wAKn93whWsaccrs_qA8sXdXz5oj4E,5740
pyarrow/include/arrow/extension/json.h,sha256=_hRPssw8grpAoVCqU4wqfPd3HRivFzMlhkMP3ULPJNg,2169
pyarrow/include/arrow/extension/opaque.h,sha256=HoNjMGE8dsyen_D1mKIOlyNeHfBXVFkBle9CrK0FxLk,2991
pyarrow/include/arrow/extension/uuid.h,sha256=ADevt5yotNdUhf-ht9xXDcC-GyLzpapjcCG7G3vbS1Y,2339
pyarrow/include/arrow/extension_type.h,sha256=5T4teNlY7ikSNCVyZ5DUpLiX5AwD0oAUlv5M6ivWv6I,6807
pyarrow/include/arrow/filesystem/api.h,sha256=wYLtR7Phx5pfHm8SKXnJUqYS-e4ffUAuKsWE8TGvs38,1417
pyarrow/include/arrow/filesystem/azurefs.h,sha256=qGfJiPlTp2EcOSUY5HqrtfE_rzjkSuB9wVuExgYOdBQ,15672
pyarrow/include/arrow/filesystem/filesystem.h,sha256=Qo3fHPF9RjM5gFMBqsQ0rsMnMwXqCACpzvq_PtboDhg,30308
pyarrow/include/arrow/filesystem/filesystem_library.h,sha256=CiZ55rYBcdvZltIjjbre4f-b8OlSZ0Xf_RMIYRSYgqI,1764
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=dX2z7NpQxXhi_Yal302jFz5jX63hadi2-rjNZOZCDNs,10614
pyarrow/include/arrow/filesystem/hdfs.h,sha256=aewVrtV752VmzKUmbXsV2WidwyFuwm9hFpGQPghUMpg,4250
pyarrow/include/arrow/filesystem/localfs.h,sha256=xRwJh4SNFE5p5ZEHolvOH5YrCZoUWXfk68YpR3gr4Wg,5104
pyarrow/include/arrow/filesystem/mockfs.h,sha256=mPQsmYNbOHYIpWeqnJGsUWROxqagJyxjYaDgHH5NLc0,4902
pyarrow/include/arrow/filesystem/path_util.h,sha256=r7xAj9YOEnkxmgACJTFHjjp21brC2cg5oF4NDZeChy0,5876
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=fpkDrqz_CkKzBexQxilPbk8Lm39Ri51lLSSZzCp1k1U,3070
pyarrow/include/arrow/filesystem/s3fs.h,sha256=AjsTeW7t4m4qB7w6yKfHY7Bz7xFEJ50PtW_Y3Fvuy_0,18388
pyarrow/include/arrow/filesystem/test_util.h,sha256=HfnLdv2YLpduh1plohOHLh5IwlgczGiq21amwUBEI7A,11993
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=Wa3zB9amkp6XJARokB7oVDFUadSK6hdsc9wRlswnqms,1515
pyarrow/include/arrow/flight/api.h,sha256=qsYeE0gW9Ja7_AiuRPcSjPG9Za8LhvnIx8xNEgGXJw0,1287
pyarrow/include/arrow/flight/client.h,sha256=jcx-D5BP9W7eEKornZ3gtJfFITpyTcM4TDunG2gt_wk,18228
pyarrow/include/arrow/flight/client_auth.h,sha256=S8w0hGHthx5trhh4Hy2IIUZhlMv-5hadI9vZr61caDw,2278
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=1fsK1K4nsZya2Lcu7ObLFmy8MySWAndeOLqbtELH33w,1237
pyarrow/include/arrow/flight/client_middleware.h,sha256=qtGBiIz-xPDGTXqywbFkpnDHI1zMbaGzg5nAFPt3axA,3026
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=htiyzC8DGhxUxP4mT2BtC2Z7YbyKIEOPMshLh98W1MA,1251
pyarrow/include/arrow/flight/middleware.h,sha256=D-QPVX66oYEtTqlhvkoKwwXJOkl46CWQ78QHMvNSEio,2329
pyarrow/include/arrow/flight/otel_logging.h,sha256=ON05jVmWvTYfUna9lNklZFvJsA93WZrndPLVWzokXXc,1172
pyarrow/include/arrow/flight/pch.h,sha256=Dd_7inDS5gHboIRsPdHm-CdyiyGfyaJWs4A8QIPZlG4,1218
pyarrow/include/arrow/flight/platform.h,sha256=SIcPkVbP-yrFvJaXw0H3FuVnFXZgbDH3KQRNqtvDB_w,1240
pyarrow/include/arrow/flight/server.h,sha256=Uid3mgf9cpE6BoNHq8V2CtbvKvnGFh5DI6nz_0I1o8I,13512
pyarrow/include/arrow/flight/server_auth.h,sha256=U6_gSIgPoMxXjKXnC-InA_nIgtOqQ3fab995KhuOwso,4564
pyarrow/include/arrow/flight/server_middleware.h,sha256=d4Lpsdn7Ge6pump7w8lGixjCM4PXPqUJB5SRgWlwGcs,3237
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=4Ovp-oUJe9MT5bM_mlN9MoYLXB_fySwUTy7C6kw9k_Q,2254
pyarrow/include/arrow/flight/test_auth_handlers.h,sha256=OMFj1T511ROS5To6lDdf77UjULqQwGiJD7MIjgPZNfk,3404
pyarrow/include/arrow/flight/test_definitions.h,sha256=Z8t86-GOHwUAljFNS9n9F_F_sgVCaQlVCzmepSSZO3g,13428
pyarrow/include/arrow/flight/test_flight_server.h,sha256=_LRrmCZ7-HqN8jgW2FtSNaAmUVvrhWNJDk84744mUd0,4022
pyarrow/include/arrow/flight/test_util.h,sha256=yW4EnCahMIRCtvE2T9-J0CgnFOZ0Dn_vrWhhOkXSCc4,7052
pyarrow/include/arrow/flight/transport.h,sha256=PeBIjWQuc4oaeHG6OytcrbCS0kKuZYV5PFyr8WUELf8,12479
pyarrow/include/arrow/flight/transport_server.h,sha256=u_0y80TFC5hbZ2TKU9FD7UKbvaXZxf4YbzkyPnBlQRs,5401
pyarrow/include/arrow/flight/type_fwd.h,sha256=1dpmGmV-kYZFcGJSt2_B7yPUHRKeTSgEHz_6sEZaAew,1862
pyarrow/include/arrow/flight/types.h,sha256=j_Gw2JmYSgk7Olx2awTodg4tUckhoVeK9BDfNqFPILw,47979
pyarrow/include/arrow/flight/types_async.h,sha256=gTu4fBjhT1vcIBzV8U_4N4I4cpjJjtcvzoDHMKI1Miw,2675
pyarrow/include/arrow/flight/visibility.h,sha256=2qZkjy01imXTXyjlPcuxXbIxYNkb8EYsj8Kr0rXy2Jc,1644
pyarrow/include/arrow/io/api.h,sha256=Dv-t-VQoHXkWlCaBqhV4JNRpbr5VPPSkidR3OuX64fQ,1021
pyarrow/include/arrow/io/buffered.h,sha256=wvHxUeDRx1JuuzsL7iqjKtfi4tSQAob5SYTG7yqXRMc,6080
pyarrow/include/arrow/io/caching.h,sha256=jzGdwSoJa6xM0g2pFC4Cy45k-qP7SU02GAMH8A-eDE0,6865
pyarrow/include/arrow/io/compressed.h,sha256=54vWktZDHbUgxil3pxn40RbcEE9UXCCXmgUOXAO8xrs,3898
pyarrow/include/arrow/io/concurrency.h,sha256=4LtT4aME7nVMW_u7tXY1lHUccm3DCk8n3cjtGkUlvHo,8197
pyarrow/include/arrow/io/file.h,sha256=mud1ILk2zK3C_jap1m0X3SWa2t_fJKi_PIDIYIkWQnI,7846
pyarrow/include/arrow/io/hdfs.h,sha256=_aRZhApwTn9FEUzDGM0eAjt358DIQhxJjHDtIkv3Adc,8843
pyarrow/include/arrow/io/interfaces.h,sha256=knb9p5UmNQlh59Pu0RNf9Vb_-H_STZdxOCCTg3P89uM,13790
pyarrow/include/arrow/io/memory.h,sha256=a3YDNpCFoDqt8eOFIInSznvtLWOH_lR_ePuqzMkKCiM,6518
pyarrow/include/arrow/io/mman.h,sha256=JynPLLGCVYj7QUoYhmpfrDxX5smbx518TosmUpsTWk8,4280
pyarrow/include/arrow/io/slow.h,sha256=E0RbN0tQQRJRIUgM--NznPmI6fLH0bxnWct1jsn370o,4060
pyarrow/include/arrow/io/stdio.h,sha256=FhklhNBsYt0fZZKBmgBfMJ_y_QPcEBkD62KQqedy7Qk,2177
pyarrow/include/arrow/io/test_common.h,sha256=rkn9wORsf-2Ux5-5QReP450OlxVZ5S63mU_mGmNijbc,2215
pyarrow/include/arrow/io/transform.h,sha256=99VKVlOO8M4HF5121bJ5iAyi0eUMqg0ndafvZ6F4y_M,1950
pyarrow/include/arrow/io/type_fwd.h,sha256=ebFvHA60xc-jpcNt7AzadAzwO-LWmFXMx6AMZMgtcIM,2392
pyarrow/include/arrow/ipc/api.h,sha256=XX2g6bMVdnN9pup7-ad7YI876hxfEOPk_5KWO_TM0w4,1032
pyarrow/include/arrow/ipc/dictionary.h,sha256=RWQcycPKBl5aREIPi90Yd8jPqLcw6ioztlHAHpj1W0c,6281
pyarrow/include/arrow/ipc/feather.h,sha256=LcTc9nldeLieMbtMidRgdvM9X25X4hEaVjZOHAYeofI,5068
pyarrow/include/arrow/ipc/json_simple.h,sha256=GHLMKo5y6d25ysYJEeEKInguimCyl_md7HFZ3Yl-x7I,2526
pyarrow/include/arrow/ipc/message.h,sha256=sAAyRIrgsRX3L8WMoQ2YqBg_mkBaUazvPjaR3nKYMnE,20576
pyarrow/include/arrow/ipc/options.h,sha256=Bs9rSLnjM3YU_O1zf1ii1_kv2YIEC4uR9YmicDTYH8U,7066
pyarrow/include/arrow/ipc/reader.h,sha256=mV4_2RaFhhQFUKfrOQtbrYpFGT-qYHKGxiLLIUhipUE,24744
pyarrow/include/arrow/ipc/test_common.h,sha256=T7q7fpQC_v8TMSsKBfhFoM0zmdQB-lDFJ5_9JZvJifQ,6543
pyarrow/include/arrow/ipc/type_fwd.h,sha256=0_J9-Ph_UwBFvwOLRi2yxfVp0Ir3IeLY5YV43Rn6cuk,1508
pyarrow/include/arrow/ipc/util.h,sha256=xJ1KaQe_wnYd9zfzVgJlLgGTVQQqxvb0xnZYutiHkfg,1455
pyarrow/include/arrow/ipc/writer.h,sha256=tLFVvlkKzo55PpYELoiEqXGRolGj2rRUknLTQhaTyZM,19345
pyarrow/include/arrow/json/api.h,sha256=QD-9FK6Ad10h03vRS5PvQWBX3jn1TQ2pTBhLv3kSRm8,900
pyarrow/include/arrow/json/chunked_builder.h,sha256=F9WcNFIXy_q9_Uc6-c37RvGgDFVPGo6bhh_OYIyPU68,2433
pyarrow/include/arrow/json/chunker.h,sha256=HPPfHXfhjAj8rtU-RmseUhEqzKtwP64UQdiikMw4jAg,1154
pyarrow/include/arrow/json/converter.h,sha256=0Iwxxsr0ACxdQlpmFujSwVeF43N3RhZ_oSrea9YV8M0,3228
pyarrow/include/arrow/json/object_parser.h,sha256=M-KYzI5UnXpMHVzLhFAcARHmX_E0Py56v7hgVHKfxyQ,1681
pyarrow/include/arrow/json/object_writer.h,sha256=Ad90l1v7OAkkyGiAP2SqTdqyAqG8GmUpz8dxn4bYIdw,1477
pyarrow/include/arrow/json/options.h,sha256=pQRNifTwZ63y6pOBumz6Z8IWHXDUAUQKBPS991Kou7U,2301
pyarrow/include/arrow/json/parser.h,sha256=uLkYKfAOeTDhHgM7FTlSdVxUWne0t39CdgBgHwlZeks,3490
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=xP604P1GxpoA4Mfoc-IliSPEO_eoeCG7bt_ZjwHU44k,1517
pyarrow/include/arrow/json/reader.h,sha256=3RyTYLnpcB9ceSGP8GoKWXqd-ZHZFXto2Fdf0nZ2ul4,5330
pyarrow/include/arrow/json/test_common.h,sha256=5Ejg6ZkZ0MdkX_-UJiKisM7evoNi62SolnDteknG2ys,11204
pyarrow/include/arrow/json/type_fwd.h,sha256=FWkyYf2wb91GygRRLXTTIFvvoztO1hfxNDq8TtivOWs,968
pyarrow/include/arrow/memory_pool.h,sha256=hBekXJc3vipso5c0kVvnsqMi4STKBfX606LV_0j_FnQ,11698
pyarrow/include/arrow/memory_pool_test.h,sha256=tK0L93rXajpMlwHuhd36chOUMMeC4X7p3HiZ2GeXI2s,3461
pyarrow/include/arrow/pch.h,sha256=09MmEJKJaxyGuyDLA8Cs2uDcK-6oUa-OFwj9hLnihiU,1316
pyarrow/include/arrow/pretty_print.h,sha256=xYwSPO02dCLPT1YlHgA_1T0FWfvhHPLG-E7y_fjnarE,5686
pyarrow/include/arrow/python/api.h,sha256=WaItEAl0O1zDMLtiHrxQSKPPvzk9s1QrBkqx-sb4J-M,1176
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/include/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/include/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/include/arrow/python/common.h,sha256=k24Fr3kOWfeygcXp-dFrNTSKXdEZVgTybqZnqfNJt_U,14870
pyarrow/include/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/include/arrow/python/datetime.h,sha256=rPOB2fYu3E7_Lb11RdXglv1tShjA_k52cyz4zw481Q8,8162
pyarrow/include/arrow/python/decimal.h,sha256=cBQOd-yW1ArcUf29lPgEgBUI6I3cCcWT4B6uwd7fTbw,6524
pyarrow/include/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/include/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/include/arrow/python/flight.h,sha256=dJ6L40ZbpUVKbyc-DytLQFaiUC9rotWFi3ofA5oX3LQ,14802
pyarrow/include/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/include/arrow/python/helpers.h,sha256=uBgiOl8b-gklCa4AxKOA-8HFZkKN9WQy7n1Pa0qIAA0,5651
pyarrow/include/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/include/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/include/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/include/arrow/python/iterators.h,sha256=9aFm6h4iTZArpr0jSCx8PNIpzmPhmHgZENio6Ykyx6w,7527
pyarrow/include/arrow/python/lib.h,sha256=aWAOSlSScduvc3gGO6aSAWcNBl2QECWEdQAZb6mIl_U,4631
pyarrow/include/arrow/python/lib_api.h,sha256=4oiKWmdhyjn5OxDSBamrukHpWwQbUioDR7X82dhgdXs,19487
pyarrow/include/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/include/arrow/python/numpy_init.h,sha256=e4zEoclXjOzRydJi6iEa0SQZoBL2xax_odZ2JApkKlk,1026
pyarrow/include/arrow/python/numpy_interop.h,sha256=NMBHwZjVHiOGujS-223OxV9V5l0niyHX3-Qw1MZ-4kU,3521
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/include/arrow/python/parquet_encryption.h,sha256=2d1-5CgmUZOFIXscD4J2nxuOkEhac0opO0PB3Jp2uz4,4993
pyarrow/include/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/include/arrow/python/platform.h,sha256=Otng5ebgyLptzmXIjS9o7tLS71emaH1doLGpDjYDxso,1463
pyarrow/include/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/include/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/include/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/include/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/include/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/include/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/include/arrow/python/vendored/pythoncapi_compat.h,sha256=5TMOeKnV-Fpx4R1WeDeJz-3EvvLWyrgGZyCLNMTVVqs,42419
pyarrow/include/arrow/python/visibility.h,sha256=yIEoKC8dabjDOxjUdLB6CiP82nhFkyGdudhLyJNgmvU,1420
pyarrow/include/arrow/record_batch.h,sha256=M4PhbyH4wLjNlOZxn33W3BuGMvRWuo4T5vma_U5l1zo,19004
pyarrow/include/arrow/result.h,sha256=RQjryjsF4PKmD94YkOQ1kYnwk3UVzqqQFQSd47QYhsA,18706
pyarrow/include/arrow/scalar.h,sha256=T32PFngSH6tvwT6fV8TsBVg5C9UNmvfpj6GRb8zVTKo,37550
pyarrow/include/arrow/sparse_tensor.h,sha256=wlyVEvtE_a0ERgsR7OHRt2VqOP2Y6e-tbe2nkqbx1p0,25822
pyarrow/include/arrow/status.h,sha256=gH-XtrYXHzG7n3Ub5Q8ARKHb1R01w2f-bLtcpI5yvQg,16876
pyarrow/include/arrow/stl.h,sha256=-lOivcsHPpTRyoxhilxgQKMcnGJE2JWUr60WnWlOkfs,19834
pyarrow/include/arrow/stl_allocator.h,sha256=L7G6TMjgleD13a4fl1cC9VyXUDQ-2fkXe54gf26PfIU,5120
pyarrow/include/arrow/stl_iterator.h,sha256=QDUTi5L6m5hBuvbcRR0PdjkQVFKyiZT5m5qcmAdYN4I,10257
pyarrow/include/arrow/table.h,sha256=hXW8OovSUEIazqfs5QW0KwI2fjCQTsoFSoR7Af8Q2b0,15016
pyarrow/include/arrow/table_builder.h,sha256=kTA3F-72RoNR_YZNmxQmatxC2cUW5feh-cvJEmi0ZQ8,3870
pyarrow/include/arrow/tensor.h,sha256=6NiMQ6l6TiPCze5vIVgbr_3QibZXqd2a8XU2gs2fuaQ,9343
pyarrow/include/arrow/tensor/converter.h,sha256=BTiYEWpV34uKMP1hgVxdoeyBlh-cqpmVaaBeIc2ARig,2958
pyarrow/include/arrow/testing/async_test_util.h,sha256=miNSGn91JvXE1NojQusPWGTINiJkk5D5xurkPNwyBWk,2341
pyarrow/include/arrow/testing/builder.h,sha256=MV0S4sm51CGu5c4HA-90PeZlm-HrVfpJJpSuSZhMxto,8787
pyarrow/include/arrow/testing/executor_util.h,sha256=3WdGxQ-F5NfkJVCR0jv7hYG5k0YtdXuOoxACY3bmY7Y,1940
pyarrow/include/arrow/testing/extension_type.h,sha256=v7FmZKoZPWEoeZbzo1wmzYlX8c6tlT7Tm7wd5SHzRmU,7660
pyarrow/include/arrow/testing/fixed_width_test_util.h,sha256=bcBgIZw5MhP7JeqHA39Xqu6FBJPoqdKs-89I-bQAocc,3167
pyarrow/include/arrow/testing/future_util.h,sha256=CyeXP1q0nDBKhFD2JpzHFOKhWhLa7ItvQ6OdQgm82Zg,6388
pyarrow/include/arrow/testing/generator.h,sha256=_2o_XnYDnMCzQNizl2mW3ijYVQyGhqdDlFrZImfLWR0,13539
pyarrow/include/arrow/testing/gtest_compat.h,sha256=Dpi16YFwo1rIcDWJp82L8S3uSQGBz7cfrAp-mA3rYWA,1344
pyarrow/include/arrow/testing/gtest_util.h,sha256=EBmbnk4liKvCF_V_HyCk-6kTah1HFgWuYPW0WmD_xGQ,25069
pyarrow/include/arrow/testing/matchers.h,sha256=DYYP4Zfhmh6sufC561VJkD_RLFxENx8iSZSa9YZTJTg,17319
pyarrow/include/arrow/testing/math.h,sha256=j15w8jqrSSYFiQQ1Y7T10ar4QnS05CpHvZ0779oOh3I,1244
pyarrow/include/arrow/testing/pch.h,sha256=CDXIaNX-5nbOaUj6udZil-7sI8UBGDaUk7ytXjRHH5w,1189
pyarrow/include/arrow/testing/process.h,sha256=gkmAniHmyI_3uO42ECd4vE05YwqkncQCrJ2RqlAe9c8,1418
pyarrow/include/arrow/testing/random.h,sha256=xLgOiCb787tAQPnQdP-zDsBudH2YdFLUHDHbVKrpus4,37780
pyarrow/include/arrow/testing/uniform_real.h,sha256=8Us2dKOV8LhsB-MawOoCBPc7eLzP35KKWVemK0UZoRE,3054
pyarrow/include/arrow/testing/util.h,sha256=s-3vR4XpJLxkSKel3H77QT0uibaj6X7LmZMGTOuO0kg,5726
pyarrow/include/arrow/testing/visibility.h,sha256=gA2cVeS6ElGHHJpuCq1o8GU3iXUnTC62gOyPihyqWwc,1654
pyarrow/include/arrow/type.h,sha256=IqLAcSmu4awdbsjqWMZmuRYSXa8u7AvT_VebPI3oscc,99576
pyarrow/include/arrow/type_fwd.h,sha256=dFIsZsPth44yNyPzSdpToFaIhU8WRvHBPl3KRvgqBr4,24290
pyarrow/include/arrow/type_traits.h,sha256=iDYsz2kJlhdy9miR7DTpXRGLiaCTbSh9BU3A-YFaNEI,57084
pyarrow/include/arrow/util/algorithm.h,sha256=okcbieN_LA2UJ29QIEQ8LcF9EblZvyu4iO9r_QK8deY,1262
pyarrow/include/arrow/util/align_util.h,sha256=0ZSxphiZMkkYipVQ8940h8jjB-0B7oKbrGGLzltPZCw,10890
pyarrow/include/arrow/util/aligned_storage.h,sha256=fvm2ZK9BN0xyJ2XCuHRDeRA5bInQqPUbCvo9yUbSZj4,4380
pyarrow/include/arrow/util/async_generator.h,sha256=ecTpivooXwNBX9BSW68CJ4OBNr7WMi8yhbD6mg5iHsE,80271
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=aVD2KiSnuZ06Jj2XBmk_cKH2umbhyJTKoDrHDmGRtyI,1797
pyarrow/include/arrow/util/async_util.h,sha256=FvGoRiKle-dv8lJrVYHr172NFJ8X-c49dGZM4YDN2oY,20219
pyarrow/include/arrow/util/base64.h,sha256=ihRHtpYtb8xH71Zu2goxkm7LKIAnQkzlsSgSUQu7BlE,1130
pyarrow/include/arrow/util/basic_decimal.h,sha256=NUYG6vfMdi27mP9zBmYQPKB2UrRpqjJFoCIe_Y_HBcQ,34456
pyarrow/include/arrow/util/benchmark_util.h,sha256=M2D-6zHAMywExxYOVZu-HskXP2tP47z8QD8OPMI3tEM,7852
pyarrow/include/arrow/util/binary_view_util.h,sha256=3zHexr0yHzt-_FG8YZ5wvH1X59M53aiQu42Mzq2k7Yo,4740
pyarrow/include/arrow/util/bit_block_counter.h,sha256=HR4DVUkayHHvzTQbBjAM6TCwSWJzFu8UJaE1YAffvqk,20732
pyarrow/include/arrow/util/bit_run_reader.h,sha256=xOv8udorWzpetz5KtUaFBSkva-Hps75HLRKnbc_OrbA,17131
pyarrow/include/arrow/util/bit_util.h,sha256=k1asaD_FfjD1vh3K5wHbkOjZWgXJp-erJur65drI2Yg,12477
pyarrow/include/arrow/util/bitmap.h,sha256=TYFzImZOknDPY07kmxd0rSw4a7Ew830hUjSiuBufHWg,17928
pyarrow/include/arrow/util/bitmap_builders.h,sha256=QzrAj7bLmAixE0ry81nA2SJWHoqkyl980r49cra74Jw,1640
pyarrow/include/arrow/util/bitmap_generate.h,sha256=LS3946GllKz4xNShS8VwA-v12rVNgvSLCY4nViapFC0,3773
pyarrow/include/arrow/util/bitmap_ops.h,sha256=Hsvv32TrufUn8lGcXShPRZGFmgwa46URagPg1w9fReE,11123
pyarrow/include/arrow/util/bitmap_reader.h,sha256=T8oLs0pEclxX1n-dlkUJY7JEsehtB4j46OmA8giXRmk,8626
pyarrow/include/arrow/util/bitmap_visit.h,sha256=PU07JCo3_618ZWJMxiTIZW6dfETceXgbjfU1YeEfyYE,3558
pyarrow/include/arrow/util/bitmap_writer.h,sha256=iznccf-wENmUl2C4qa5P9tyCUnarQKYqpoGiYcB7DR4,9669
pyarrow/include/arrow/util/bitset_stack.h,sha256=10k45pz7ZzWrJwTuGqcLztVLg71wfFoabykodNsnGBw,2865
pyarrow/include/arrow/util/bpacking.h,sha256=e4TwSlBz3V6YjsWGUSMwi0por6GtEN07t7LtjuSopeU,1209
pyarrow/include/arrow/util/bpacking64_default.h,sha256=Ov2SA9sQmohXSHw0Rb_zytyVQ54TJK61V_smIZMdkac,202632
pyarrow/include/arrow/util/bpacking_avx2.h,sha256=WKb37t4ch1eOkeNGySTiKAG1lfCN8Cvtnjy8aFHt9Jg,1037
pyarrow/include/arrow/util/bpacking_avx512.h,sha256=H3iqF8SNMT4szyzvv6gSwdXMmNitTqN76tDELLYG9vo,1039
pyarrow/include/arrow/util/bpacking_default.h,sha256=OjfkZlZhuOYn0rXQmABEFctBSK6TQDDATrUUAktYBGE,108011
pyarrow/include/arrow/util/bpacking_neon.h,sha256=aukHePChI9vXwUjSCXZ7YMjQmHh7xCtxm4iHKlVs7zU,1037
pyarrow/include/arrow/util/byte_size.h,sha256=Z9HxkPG4DAMSJ5bxpglqUP3UP-vnMViRb9KgTrOhh_s,4085
pyarrow/include/arrow/util/cancel.h,sha256=LwAWfES1tiRHZCyYiOqpxSaRVm71_PxhiLOldIUN78Q,3777
pyarrow/include/arrow/util/checked_cast.h,sha256=0XkAxq3kuBxU_pMZ_r6rmgovb5zoFHhq9nZNuWKkgVQ,2137
pyarrow/include/arrow/util/compare.h,sha256=aslVAwRy9uF49L9neBjVmMOVANewLp_GgadkieX7rWs,2044
pyarrow/include/arrow/util/compression.h,sha256=Rs2N_GisDUu13jxt9sbaz5GutlqFKwEqRxxb2ocqXe4,8668
pyarrow/include/arrow/util/concurrent_map.h,sha256=hmRw2luX9OVbYwIyMXc3fEgof4An4owLnWrAsKgXKpM,1843
pyarrow/include/arrow/util/config.h,sha256=AqLZ_8xcwgFeEAkAEVlVX6ziax5MqZKiNh5k_hYh1VE,2351
pyarrow/include/arrow/util/converter.h,sha256=9Wl7M4FSGKwj_jZnVDELWwkkNcd8rJmlsAeOwZF3W1Q,15048
pyarrow/include/arrow/util/counting_semaphore.h,sha256=pjZ6Sq7cAbmTVwIeSoCS6je4ru00KKXHzhtOUpxLItE,2311
pyarrow/include/arrow/util/cpu_info.h,sha256=Cg7Alr-NR4E89JZO9KlAo5bH60gVUcvpqBOKZ_yUF3Y,4078
pyarrow/include/arrow/util/crc32.h,sha256=mbAGpVjfRlJS1npQaAgDBWJYe6WeflXM6J-ZBO9MSTQ,1373
pyarrow/include/arrow/util/debug.h,sha256=Y8UN-qs-hijFAWr-LKoNq1dpI2ol-krDuy1xTCVYgb4,1000
pyarrow/include/arrow/util/decimal.h,sha256=2czGV2-OQgv2KjmC0kzheFWk3LzzSq6nfxvhldf5_vM,21366
pyarrow/include/arrow/util/delimiting.h,sha256=ZFc1v-DOeBEBfWYcN4-cnxvQMToxeGJeJZ63Z0nrWgc,7498
pyarrow/include/arrow/util/dict_util.h,sha256=p-_WX8G1NM-WB158LBV-aFfd7s1l9rlZi6a--dLStEk,1014
pyarrow/include/arrow/util/dispatch.h,sha256=_-IP_k9Cdw6p58XAiuxjXE9G9-na6vb9HoYXBP-WRl4,3350
pyarrow/include/arrow/util/double_conversion.h,sha256=AMG-JfVeM9bRTe43j1iGNPJDU1NNZbEwwtE_E-KmuMM,1275
pyarrow/include/arrow/util/endian.h,sha256=IPmpGTRSFGusQUA-lb72B911eKr22mIYFr91svLVowg,8421
pyarrow/include/arrow/util/float16.h,sha256=yCkcuOyYL9qBq03TmGhm9UCE5o9CF-dfG3M3pOzn1f4,7431
pyarrow/include/arrow/util/formatting.h,sha256=rxZwXmqlNSsJvIQYsFuUZgyms8HgDnwsrHGhvrVYfFM,23222
pyarrow/include/arrow/util/functional.h,sha256=vVBnLo4584uiCYaRQSA-2BzzsNSsyjWbEdgsF6L0a8c,5772
pyarrow/include/arrow/util/future.h,sha256=kWHhE5nOrI6qt0rJJCFQdVOP8yCf-Px7oo4TSEKYmRg,33178
pyarrow/include/arrow/util/hash_util.h,sha256=pUOiWTK7x1Gz0zzhTmB7wQDOaUo5R-J3qHt49z7rEx8,1984
pyarrow/include/arrow/util/hashing.h,sha256=wtTffg2FDX9sFQpP6nOhnSXHz13k5SufTvhAsxCx1Ns,34166
pyarrow/include/arrow/util/int_util.h,sha256=1l6c85GqyP_k5ZOsOyqu0mgaA9YBo_JJe4fv_4eNEJA,4996
pyarrow/include/arrow/util/int_util_overflow.h,sha256=2z44sPGDtAVXvWgVOI85H70cKo_FlUeue-c91aeIfWs,5013
pyarrow/include/arrow/util/io_util.h,sha256=ThGNEUjr7X-jkqbogiRgu57m8FHNvwsLEOjH3RmYuH8,14161
pyarrow/include/arrow/util/iterator.h,sha256=pfePS_Im2bQGmH_L5NZxWNzmKtyq9MeMEwl_ucI-0dg,18936
pyarrow/include/arrow/util/key_value_metadata.h,sha256=m-o5pRI-eyo_32FVsr4byAdE2zcUKul9ROsqE2Oj9Ug,3689
pyarrow/include/arrow/util/launder.h,sha256=SSTWzD4H-Oy0TExl13n4sWqHiRLlKtBxT22r7JZZ7ZI,1081
pyarrow/include/arrow/util/list_util.h,sha256=qO0VnbXcxWfgMuZ4IOYsQk1Jo83EQI64MP1uK-gyWgs,2083
pyarrow/include/arrow/util/logger.h,sha256=FeQZRydUwfQWgEhMZAQpAPk2d4xXw6e4Wo6lZmqYH6w,6879
pyarrow/include/arrow/util/logging.h,sha256=8HU8ApZ69TFLMUrK-aonN0UziAnABKF0ZO3r-D7taos,9971
pyarrow/include/arrow/util/macros.h,sha256=6ZUfF1P69Wsp_C58oe4IWiFjB8nMNbFRr3uwMHgibZU,9575
pyarrow/include/arrow/util/map.h,sha256=dRoAZDkMFDHZ4rUlGGxilCN0qVW969S8IbNDYMhmXpw,2539
pyarrow/include/arrow/util/math_constants.h,sha256=V4WHS9hLZcdYq2Z3bb4jFiaFSpCUuXqPhF_RooxA0ow,1144
pyarrow/include/arrow/util/memory.h,sha256=wXr9qPvbRTNJNYDq92iEVGgxSzWEqAu4Eu-Kb1qHRVw,1609
pyarrow/include/arrow/util/mutex.h,sha256=jANHLdRtEDUTIoCYa7kBFRr2fTQlhkvabm1xCKkrFSY,2639
pyarrow/include/arrow/util/parallel.h,sha256=PkkDh5qughPrt47wrO4URgEsxYS-ovMN1fI-Ue6gmVI,3921
pyarrow/include/arrow/util/pcg_random.h,sha256=Wb4huSnThKxOkUJTPjIUZBebkjs-FlzDVbjZjK_bTzc,1285
pyarrow/include/arrow/util/prefetch.h,sha256=2clukiLMMeCJ7R9QNeoogx8TYDUtta_qla-LvkKSJMw,1282
pyarrow/include/arrow/util/print.h,sha256=0HAu2Eq3Adpb9IpAPdHcl2aaClY4Ur9I8ZlF0h27P0E,2521
pyarrow/include/arrow/util/queue.h,sha256=vs-xE_jGsLwiWsCiSIshMMPxm-XKxThG6fRLoghFEYQ,1046
pyarrow/include/arrow/util/range.h,sha256=lx2Pwl1BaYNQSJg4VI2ej4JkCRV2ecYDt8W3ac-4AUA,8784
pyarrow/include/arrow/util/ree_util.h,sha256=WCM3vA6YQTbzcO6IP4TeOwUJxdddZ-nMogPQBBVGbUE,22977
pyarrow/include/arrow/util/regex.h,sha256=9Ilm_WKJawvl0MbRXYFU6nCHqiAnctxjEXZDgTK99FQ,1793
pyarrow/include/arrow/util/rows_to_batches.h,sha256=5foVd7Rxoqf7-g5U_yiMyRcVb4zJEgBNrXqpqsoCtXk,7283
pyarrow/include/arrow/util/simd.h,sha256=6Use6liJ1LrtDRAYXoh3fJhOe_O9XU13KupRQ3FKPOo,1730
pyarrow/include/arrow/util/small_vector.h,sha256=aP9xti38tT1JxLqyyTfa1iE-K8UUFitVkz9GEnOtPJk,14932
pyarrow/include/arrow/util/sort.h,sha256=z6QTFJTKranZSLz3ov_7cgdAUUCRBPOVpz6juqPsmiY,2544
pyarrow/include/arrow/util/spaced.h,sha256=GxDNNOKr_wFJ77bmwFPzxLFQxT-EHqreUOYTVkqiHEs,3665
pyarrow/include/arrow/util/span.h,sha256=hHP8_YM_-35H7ebmm4ndwOegRE-H8w2XgvmfjMbFYHQ,4430
pyarrow/include/arrow/util/stopwatch.h,sha256=wjaUvLLMmPxAMo010wde0aDkSutbaRuptkvxlD0_PHM,1449
pyarrow/include/arrow/util/string.h,sha256=Qr6Uw8w0R5QTVllapJlhn4xdd8mYk3b7cqGdmVnLlUk,5929
pyarrow/include/arrow/util/string_builder.h,sha256=QRCI8UThrXhrg9heM83Ktdxeec3WBLnbzt0bwsQbMZQ,2743
pyarrow/include/arrow/util/task_group.h,sha256=ndcevZLV91JlhUjTy0STS6vgfrnlNIds6PH9CogPbY8,4468
pyarrow/include/arrow/util/tdigest.h,sha256=hrxY0zRFR5KdFT6LYLxOPShbopgkZnAYCKrEdF5xX1s,3162
pyarrow/include/arrow/util/test_common.h,sha256=di1zrj8WqO82vFlPxZicFqLgm45YD6npiruZrVGEWxM,2927
pyarrow/include/arrow/util/thread_pool.h,sha256=cb0D3t6SBnB_H6ujjQ0D8xiwKqLZEf88lkaXEPxd8Vw,25046
pyarrow/include/arrow/util/time.h,sha256=Qas0nobSX9zpGDHTVkEy4NiRTZmapE6huDafS4B-b9w,3071
pyarrow/include/arrow/util/tracing.h,sha256=-7AEJ2qsSlJ3IMwQ4Gw_5KV0R1UPAmF-YHfFJchO4po,1331
pyarrow/include/arrow/util/trie.h,sha256=B9ei9sN4XgL857xEyF0Ho5EGnqjrNKxJDgkXqqHcS8U,7364
pyarrow/include/arrow/util/type_fwd.h,sha256=JPBUVMn_mAAvXFDrr_k1aYvZD4LBaLP3PryqhLyvd98,1885
pyarrow/include/arrow/util/type_traits.h,sha256=s1CoxKll91XgTlUJLQObKZHN3vJM-VX2WakWoXvzJZk,1777
pyarrow/include/arrow/util/ubsan.h,sha256=LpyqTqFLkxflIzXjv-5FEd4HvVpoh59TQX_WJ12PR18,2904
pyarrow/include/arrow/util/union_util.h,sha256=Er6Skg48w3vcVZJmBHkWqU9sfeRyyrSO7A9JUU-qc-w,1242
pyarrow/include/arrow/util/unreachable.h,sha256=z23Cp8loRsJJFdIx__AJRBzQzN8dAOnRmktsMIqW9x0,1100
pyarrow/include/arrow/util/uri.h,sha256=7DAZEXo-_N4bLm9dyPlpCge1WgbLRbm35Zvhu_T9Fzs,4005
pyarrow/include/arrow/util/utf8.h,sha256=Bon6sFrfWHTRc-aTxE7AIznWG1UMLsr0iukjvg209lM,2090
pyarrow/include/arrow/util/value_parsing.h,sha256=B6Vj7vDau1onUrUWLCDGgd77RlfSNZYW2uv_wyf5Ns0,30940
pyarrow/include/arrow/util/vector.h,sha256=eXtmyXVqBmM4nTrn-2eyPj9uvPs9FFRUcT7ylsCtR2g,5869
pyarrow/include/arrow/util/visibility.h,sha256=_xiFZzfiAuQj8mQJWspWmCq-t-MV_ZUz4NNiEh8D-Mw,2921
pyarrow/include/arrow/util/windows_compatibility.h,sha256=Uz2JwnhEnqXRMLRXmvbCX558Wfv-0zE1MWL7iBzRUa0,1294
pyarrow/include/arrow/util/windows_fixup.h,sha256=0Q0aniGbX6vG0IFVG-1PKUg7LMfOyQ3avtBtgk6qWxA,1487
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=kQtEadAfKO3qYjALi1ywgCaE0ov2aGQBUFdMS1qQC48,6318
pyarrow/include/arrow/vendored/datetime.h,sha256=VP3LPk99XyPSS6rNRRIdtY621QBZwEzoJnVNf8Yfihg,1130
pyarrow/include/arrow/vendored/datetime/date.h,sha256=wg6LHOPie1Ul-uhW1aMaaJq56ENAEW99mVPUNTtfG8k,246053
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=Nj5HW986tXE6i_HPt6FScNRhHjn9N4fKCNkBqs8hRMM,1691
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=YZFydjZZWZbGftsIa4gUMinNhGTyINCvCkAfT9V2U-w,88155
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=z_VF64I4islcSPxQlUqXz-Xts3IiekINYkyxgFq1XDw,11021
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=5y9-u5WTzSxSMMn31oyed-qXli8vb5SYORHT-3QDVnA,1032
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=ug8vS5I3hPBzzWy8oNbgFxI2TMfB5lH7h2GqA7feh5M,4444
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=2JlGD5vJLfiFtxZPhQ_cxgAp9HslZxsCv75mott_vtI,6103
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=0ngMtGe0Z2w-rRx02TXZce4oRwGkUIyqTTXaWDQ3uzc,3145
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=wDWYQCswEEMKvIqSrmNV6c8rKuRwEhxjuJfG1D3vMM4,5227
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=gxyJNQw6njGyORh7oKiRbeMXOTG7wFhaGLNNf2p4SCE,1838
pyarrow/include/arrow/vendored/double-conversion/double-to-string.h,sha256=LgPPmQstU7ODW1sYBQqPiGqqE9rn9cXXhv4NepiLcMQ,24397
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=mp-y_ztzGDBZpXrXob3GxoJlVQ2d72HraY3HrBhfySg,4212
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=G8xnzmSUSewcLO_31lSDFwGwtYX-UNrPfBnFET9CQBE,2886
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=PkmBx5i6U4mxaVNAHigdSzFSXwy5HnQVToquqqn6wjE,15730
pyarrow/include/arrow/vendored/double-conversion/string-to-double.h,sha256=qVlN6Ipax1SxNPpBLDgwVWiwf9hP03WhQ20V07cHC7U,11146
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=NJ9MHQpDfEtOBOeb75_p6LFmOnkDoe83Lyt7MyR-XxU,3162
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=-4-s2NNi2xpcVkQ1FC21SelDLJoDM_O0xPelwq9mevk,16034
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=XtzT6-JHWmGxRuuMnDM9_0AXbOb4lzGWPyTXqdH2mGI,20433
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=79Y3YTo5rbOcOJlxfzbA08IrwVXRjQyNwaTw1LSuo3M,75455
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=pUPqr7_xnyJ7kfoQJPzRHwPAoPEX3XRW5Fou82CCeYE,29419
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=nco_jVnzpnDASOnTnPFE8CCj9uwOWhyV-kBgoBmnf8o,3164
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=D8WFFrtN64lU8XdNOdfSKwMMfIHxsqqbbATfuvSA1wg,49239
pyarrow/include/arrow/vendored/strptime.h,sha256=6IG4iT_rLr1Z-r7FcitdiZyEQ28slbb8r8ty5ik57N4,1247
pyarrow/include/arrow/vendored/xxhash.h,sha256=ExWtVPlfaed4_G728m0ZVyo_x78z9lx0E_-aZ_MB0l4,862
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=K0vb7lnmVPp4CV0BbzcTaZgk1Z-QybZFg4dljxt8JBA,259869
pyarrow/include/arrow/visit_array_inline.h,sha256=UtSCAHQQfOIMqAD-Q8gXRqqSOJbDF7hbIcgXx1KHcNE,2510
pyarrow/include/arrow/visit_data_inline.h,sha256=8o6Cq0QwiQBTNAvX7S6BfzwHRkT_Ti65QFJfi1jeUas,12797
pyarrow/include/arrow/visit_scalar_inline.h,sha256=hxfWYgeRl22v7iANSNoIR8RDCJWAYlRRSfhbEIPotoI,2487
pyarrow/include/arrow/visit_type_inline.h,sha256=BpparmGLAnIY17kqWic7UpMNjwjBex3F-zFXRlftzLs,4504
pyarrow/include/arrow/visitor.h,sha256=-EL-W9V3NZIn3sxAEXjF9N-Xae5Gz2O6ScuS6vEzuJA,8881
pyarrow/include/arrow/visitor_generate.h,sha256=MRXQDAazSWim9pqablmsHkHIgqKJfNX1fFUfMNDSU1s,3399
pyarrow/include/parquet/api/io.h,sha256=D0-NRwdb1FrXH8DKZsjZ8MtfZjjUaboR_lv1iFLbr2A,867
pyarrow/include/parquet/api/reader.h,sha256=njU4e2U-9SWf-XtxKdwRqezF-hLF5dAKIWUZFmyawa0,1239
pyarrow/include/parquet/api/schema.h,sha256=rDl6YZ_-iwqcmp7t_rSADxNtiJqJtdl_bHlbOJfCtE0,876
pyarrow/include/parquet/api/writer.h,sha256=wR783UqPiqunq9PY90ae-3lCkuQlScWdFbanzal8NpI,1032
pyarrow/include/parquet/arrow/reader.h,sha256=-atpa2hZnTa1t2LmE-BPnuGDVdtEw_zJ3Xsg--mz188,17833
pyarrow/include/parquet/arrow/schema.h,sha256=EBNwRSV2xGo9YQcBQm7lrGrsRoKSy6dkflJlVxxjSKc,6388
pyarrow/include/parquet/arrow/test_util.h,sha256=TqKmN2GuFlVgujpw-l9xyN5L-W3bJtBXyEK9GOk6Hwg,21103
pyarrow/include/parquet/arrow/writer.h,sha256=8zrNxcd7a6D672StRHt_HJG_WazC1v32bETYVnqv4NE,7459
pyarrow/include/parquet/benchmark_util.h,sha256=8bubjU8G6X38FIRNvymuzKsEWua2vclIXF6BOiDm4gg,1805
pyarrow/include/parquet/bloom_filter.h,sha256=vRqYGhumsQ7pf45UoqmF6IW7v8AMtl66V4SyPsXnNRI,15362
pyarrow/include/parquet/bloom_filter_reader.h,sha256=rYjN1t6FheFqk0_M_uRUw5SVjbZdiF8BDRcjkkXom7Y,2960
pyarrow/include/parquet/column_page.h,sha256=XmbGix_mfx1rstDV-9MsqYscRa0O575Q04xN_415wkE,7121
pyarrow/include/parquet/column_reader.h,sha256=tYrsQ6cedJh0xfndGNHScSLhCkV1b9Y0SLL722tvwtQ,19378
pyarrow/include/parquet/column_scanner.h,sha256=kEe6sFLctNQdYHWSe8kZMJn2FFwPpQ9YV-VsHFZYuWo,9127
pyarrow/include/parquet/column_writer.h,sha256=IP_Yj07NKliBl4_H4HhCthdyibEhJFhlSK1ja0C6vGs,12598
pyarrow/include/parquet/encoding.h,sha256=0_tsPq-p0qwY2gU1zvuNa2yZ-hRU7uouum_YmGnlj6U,17060
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=mAJgC8sRq8jT_8vC8aC7gkg9iU3ULMl7t64rQUdxXc8,7216
pyarrow/include/parquet/encryption/encryption.h,sha256=I8JqHFXAieeB2_6tMs61FRJZDec2cavh8wCPOFbRwHM,17128
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=8HQG0om1FEes8bOWBnRlVh9ZBjOIkRysBzix8-JLp2Q,2257
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=6m9OPhdKPaLQ2dk_-ntCKWf1TzQITrEXLZMW1D0KyJw,4729
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=UX5QR7SdyidywA8V8m-zcorleRmXMqFNQmJ8iG0Eg_4,3846
pyarrow/include/parquet/encryption/file_system_key_material_store.h,sha256=S16ZOAAZnH2q-k4oqm2bQAzR8TmdCIWOVAunaDIMeVw,3662
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=iXagnT2x08O3md_vKPmYzwrZqXdEutkIUGVpO4drTIw,2289
pyarrow/include/parquet/encryption/key_material.h,sha256=FLjsSciNllhH2rfuh6xNGSvwPuNpHE2dTeFeAdy1cEw,6350
pyarrow/include/parquet/encryption/key_metadata.h,sha256=9dLFh5ppO4icbYkJMNpyT1s43wfAY0UIMdgNkzfiWA8,4094
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=7zisONi_O_qn-N9Qc1Ady-X43BJMktV7SHz1Y6h0j-k,4683
pyarrow/include/parquet/encryption/kms_client.h,sha256=0AzqB7Wgr31XPiPihxFGVIYcFo44oz-ydPuZcMMn2SU,3244
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=OnnMzSQewEckWA1vpsb2g4n7LBQ9zzDueMJi988dV6g,1331
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=V0XBLFK9V8AeJ0yRvnB17mBF9movGmkItBnVylLPvbY,4048
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=YjOgyMRAaNDhAsaM5-LFu8PnHiOI1Jx4ODbM7prs_JQ,5342
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=-Whn4W6ydKU0UN6DNf9qmZ-Z6RZX-H4H6H9XefQu_sQ,3615
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=vBfZbv_lmyUxr9O0fbj7-SubspRAkxalwxIaApVJ5SM,5232
pyarrow/include/parquet/encryption/type_fwd.h,sha256=Y8CtkdgEEkelQiGOHiFxNm0jAtUl84UQS7ucBOK8tmA,983
pyarrow/include/parquet/exception.h,sha256=HbahtCNwrFdUeWwspT-2ZrRryxbRVBsBG37LX0O_54o,5757
pyarrow/include/parquet/file_reader.h,sha256=RpkF2dVk3GWoGd0jTtTkC73hma5nnBUr7QipzzqxrVE,11442
pyarrow/include/parquet/file_writer.h,sha256=iY1zWKySLKxJoDDn2BjOTSE0H8Wj2OSRVzO3nEBpxlw,9588
pyarrow/include/parquet/hasher.h,sha256=bDhLFfpDeTO3zK6hpb8o3Of_iCx_YooSlRrx2N64gg0,5358
pyarrow/include/parquet/level_comparison.h,sha256=drcd3Q01X1fOv5RezyxondOVPt_vQkc4X003dUzrhig,1344
pyarrow/include/parquet/level_comparison_inc.h,sha256=FxnbEl-Hv2XGSHfOxkdqA4A6s0wBPHt5Q3UEcNSUHsc,2555
pyarrow/include/parquet/level_conversion.h,sha256=xWMMG8DFqxIx4EVevtiFdLtiZhKT3KajEd3EhIJv7lM,9648
pyarrow/include/parquet/level_conversion_inc.h,sha256=UjK4ppQFhMnQxXCIp1wm9HpdjXPpa-sOUQA0EG9AZkM,14515
pyarrow/include/parquet/metadata.h,sha256=ExEqTcMTqMiAfpqaN7YUD-JUKthHyCp6B-y7FeeOxh4,21182
pyarrow/include/parquet/page_index.h,sha256=ZQRBnOYPCFU-HqisHz3PU6hfa74675Jm7VNWUiwYW-I,17693
pyarrow/include/parquet/parquet_version.h,sha256=nBSNpw93YrjXg_Q2wH-ZxAfy94miekV9adWKUxzomms,1195
pyarrow/include/parquet/pch.h,sha256=GCCeQqUf1FYv0rA70WfR3Q9wS_3STE5un5tTgRuOjEg,1277
pyarrow/include/parquet/platform.h,sha256=louS4-l00S0cBiFHu6Pm-dn26qu7OuOs9WfIf__jRh0,4010
pyarrow/include/parquet/printer.h,sha256=TrztCNIK16tEjlaZhWch3jdTayHMPzjBdJaziVBefRk,1594
pyarrow/include/parquet/properties.h,sha256=JrxEXOGrwQaxghdCBrvTwf0a94rW9lwqD0JZ9ff9Eac,49999
pyarrow/include/parquet/schema.h,sha256=BruRfhUDpZgk6zDAnkACNXa8PZVEQC4zw_VCEdksfBk,18716
pyarrow/include/parquet/size_statistics.h,sha256=3Tj8RKUY1ghn5Z2sT5DzrBFOUZ_ArgahigdW-QjkYzI,4429
pyarrow/include/parquet/statistics.h,sha256=qY3w11PQlus9wWCHuK0OPiVzpfyg5m-uGv5LEZ-GpMc,15558
pyarrow/include/parquet/stream_reader.h,sha256=sJ_wO06AUopUtr24Y_O5FDCTQHdA_5A35HIx2Uq8AWs,9094
pyarrow/include/parquet/stream_writer.h,sha256=k7qykhypcl_c394biPzy3RaAxIDEcZNWCTwKGnQrMHk,7748
pyarrow/include/parquet/test_util.h,sha256=-AZcXyBGxuzTBRGl7yRgqzQPe9qk11-qsZi8dev-ns4,32014
pyarrow/include/parquet/type_fwd.h,sha256=lZlEghydGnN3HIL8rwUpwZmviDWlv_rZcd3FTWX7eQQ,3064
pyarrow/include/parquet/types.h,sha256=FDTPuV62hv5iitmC5UYqPDep180U349LCjsf3n0x1u0,26302
pyarrow/include/parquet/windows_compatibility.h,sha256=UqmjdtDSUHYXXSN7FfO5KqS-92ukyDG6iKOThvjcZ14,918
pyarrow/include/parquet/windows_fixup.h,sha256=R0sEz8lear84QSG4IkpGM_OUTXtMOwHLXRNAToREh_4,1081
pyarrow/include/parquet/xxhasher.h,sha256=xEbgF2yqriXsdUJBP3KdjMb_0AfpTzx7z6ZlmRLeIOs,2124
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=sOPY7w6CRs0PDyh-fbY0DnqTa8oVwBZJDkz6oA5oYeI,5178
pyarrow/includes/libarrow.pxd,sha256=_e0RJP50TSuHhMz568WfQR5SpiExqXg3hyAu5oxtIuw,122148
pyarrow/includes/libarrow_acero.pxd,sha256=Es8B0rG40J8Od1YkSfUBF1g-n4PlsD0GEe6h-zDTXg0,5416
pyarrow/includes/libarrow_cuda.pxd,sha256=YKMKTSNWQlUvInVfakoe3uIh-jJjS-4QZ-tj2FHT7Ng,5051
pyarrow/includes/libarrow_dataset.pxd,sha256=0GxhTlgRpT1TV8-BXbfwF2viZoO3Q7kJexWmqi6eeZU,17551
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=uo2ZVaCCvcDzWK7jS7yW66Ap0JafdUMxHrsCfMOFIjA,4641
pyarrow/includes/libarrow_feather.pxd,sha256=hIVe5CqIhUjc2nvfq3vjo1XoJm5qaXNZ3a1RWw8zS_U,2190
pyarrow/includes/libarrow_flight.pxd,sha256=vnHKZ2MW01Z6IBKryiulFiqncO7hxwAw1Wqa6qH5HYk,25284
pyarrow/includes/libarrow_fs.pxd,sha256=IJMJq2ZCCduD9XPm3tXP18HyjrIicNqY1Z6gl2ZUn5E,15390
pyarrow/includes/libarrow_python.pxd,sha256=DYOje_snYMEHDN_hMtSy2anYE7dwBke8arFzIGpE3eY,11546
pyarrow/includes/libarrow_substrait.pxd,sha256=j0pCpCI2ibMsMXAvQ-kXTyyNfHUv0A2odoBnoo084Hw,4161
pyarrow/includes/libgandiva.pxd,sha256=WYxsELoA8UxY6LJ5aVyaxUzsQDxFvwcxQJ8mVv0i6zI,11836
pyarrow/includes/libparquet_encryption.pxd,sha256=ePyhBM-vE3aVQKS6ccpbDbwnhc9IJP-x7IxFsPXBWT8,6028
pyarrow/interchange/__init__.py,sha256=hFVl1PV1F4Ovb92_NbtKgDsrqRquxi82guDjtkK0CQE,865
pyarrow/interchange/buffer.py,sha256=YANe1dybRIzNzd5x3FGoQb0DPfmzyzZOfvFHDLXF5CI,3466
pyarrow/interchange/column.py,sha256=jmhpwkBMvQLRM5Br3jQxIv7fZY929OzqBv-Ewm04WFI,19899
pyarrow/interchange/dataframe.py,sha256=tOTq0H1TBOfzFrEN10GPAJnF4iyopd1jF_zKamvMqRQ,8622
pyarrow/interchange/from_dataframe.py,sha256=lGy4ZHtP61gfCJQScVx4HwalvqjIhQU-Ar6saDJsE5Y,20323
pyarrow/io.pxi,sha256=a-b99BwXb5Y4MkRCNA_HTC7aC0oABOot5H-X6ipu8Fk,89413
pyarrow/ipc.pxi,sha256=Iy0bUc7H3AWt6OferICnP5qqAYCygIT0OivDsxkB-JI,42484
pyarrow/ipc.py,sha256=0pKl7G5ZpygvQZifFriOpzTkS0jB4qbGvUksGVVNYg4,10392
pyarrow/json.py,sha256=bgpEq-S1JmTPNsmoBsBdz7R3A9Y3lwfpniQyadO8klI,888
pyarrow/jvm.py,sha256=hOBj7jSA1E45ZdqMMehr91Tgvqwo-XFHEPKttESHWO0,9928
pyarrow/lib.cp312-win_amd64.pyd,sha256=1MCnLbetlu3ue1dQG57YuhSCXvrfvp17ze29A1poKfo,3523072
pyarrow/lib.h,sha256=aWAOSlSScduvc3gGO6aSAWcNBl2QECWEdQAZb6mIl_U,4631
pyarrow/lib.pxd,sha256=EeW3cFfIJeWlIyQRztmH6QdjVV2YetU492Vr6nwmLl8,18341
pyarrow/lib.pyx,sha256=4_IfhzUCb6TaIkrxaOMxREKQWGMVjAr7ld_JAxQBp48,6317
pyarrow/lib_api.h,sha256=4oiKWmdhyjn5OxDSBamrukHpWwQbUioDR7X82dhgdXs,19487
pyarrow/memory.pxi,sha256=ZJwMEEJydvnWiCvhHSQen4Z92YqyZ7GqD3Km4DOOVzg,9204
pyarrow/orc.py,sha256=y2UvVsf7xgkUVk447bHn14x-m5uZX3UE8jIQapOA4DU,13002
pyarrow/pandas-shim.pxi,sha256=UtXdX0NPFXISTYWOmvFkMbMEZ80COSz1cWkjm8grLTQ,9077
pyarrow/pandas_compat.py,sha256=y77Bb5_os-E6wIDe1Zjw5wSUKWSeYFoJTMCvjzMzTHM,47005
pyarrow/parquet.dll,sha256=6IL11RjAitv4N5-iX2WLltCk7NIAnyzLd46s36bRVdU,6086656
pyarrow/parquet.lib,sha256=LvKL6IS7mAcE04Yw6ED91bs-zS_luCUcoQ8I9cw2Brc,713690
pyarrow/parquet/__init__.py,sha256=MP8hmY5y1-LXoEUg6VinrGfThZkw1HAwUIL4ddrSAxQ,842
pyarrow/parquet/core.py,sha256=0Jc5umagfTivl_vo-Qh54RfpNHQBR5UAwSIFoS9A9G8,91604
pyarrow/parquet/encryption.py,sha256=8XjYgPw3KhU7eYuXj23pO3dJArm97v-1AAHt4WCfA3A,1176
pyarrow/public-api.pxi,sha256=eOStTQZatUyzYnR9ylr3sBzAEmJQqCSx4cRuXoTHHpU,14507
pyarrow/scalar.pxi,sha256=QA8kpStNaJzjqEqucruJjNhj3IxbLOuQRAn4C5qfZuk,52175
pyarrow/src/arrow/python/CMakeLists.txt,sha256=psEMQqIKWjI2J7OIx0vE9TYQUNXHfTWVdL9F6GVGrZI,874
pyarrow/src/arrow/python/api.h,sha256=WaItEAl0O1zDMLtiHrxQSKPPvzk9s1QrBkqx-sb4J-M,1176
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=UqlYFeDCZ-ok9P49NfjSOGgrIpUiUwzTnt91WLC_w5M,98109
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=DbOoJJai8YeS1JKBCloQlgzztwv1Kkjv3rF86nUK7ww,1789
pyarrow/src/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/src/arrow/python/benchmark.cc,sha256=-bPstUW6eQ843nOduN6dFYUfYaWeJlL8PctHJZkRQYI,1331
pyarrow/src/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/src/arrow/python/common.cc,sha256=1ohE6hwR_6wF55NRu8GmVzTqwCq0u3YBwVH6QqfWKmA,7837
pyarrow/src/arrow/python/common.h,sha256=k24Fr3kOWfeygcXp-dFrNTSKXdEZVgTybqZnqfNJt_U,14870
pyarrow/src/arrow/python/csv.cc,sha256=sosW1I3yNLLmoScv8o8ECYzLUB8ZiEboeDAstC8SVZg,1865
pyarrow/src/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/src/arrow/python/datetime.cc,sha256=gAQFEQ3d_YmX7vH-yS79-dHNUqoyl4TCKv8Kkad3uQk,23664
pyarrow/src/arrow/python/datetime.h,sha256=rPOB2fYu3E7_Lb11RdXglv1tShjA_k52cyz4zw481Q8,8162
pyarrow/src/arrow/python/decimal.cc,sha256=HlJvygomZONSCYpsMoAqHqgSLJVX1CTL1wU3vzRRLas,9856
pyarrow/src/arrow/python/decimal.h,sha256=cBQOd-yW1ArcUf29lPgEgBUI6I3cCcWT4B6uwd7fTbw,6524
pyarrow/src/arrow/python/extension_type.cc,sha256=6CdWbNCAZ0DsUqSYYHU9vsUxH4w5nPx2H3rl4WVBGps,7077
pyarrow/src/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/src/arrow/python/filesystem.cc,sha256=TF2i8-laHGZJgDizwsMMBxPi0gTJEiC8RB9A6MK_zfw,6358
pyarrow/src/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/src/arrow/python/flight.cc,sha256=siXlVvGctnqCABP9wAwElqS5VRJYcYy6s-wW0eWRMIs,14583
pyarrow/src/arrow/python/flight.h,sha256=dJ6L40ZbpUVKbyc-DytLQFaiUC9rotWFi3ofA5oX3LQ,14802
pyarrow/src/arrow/python/gdb.cc,sha256=C9dUHJzNPK5Ue_X-w5JHVbKni86qDc1IUssitdYlljg,23176
pyarrow/src/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/src/arrow/python/helpers.cc,sha256=VPibcENe_XgklqhZ0jvBLUApC2bfdf7_DOWx-tvXA3o,17128
pyarrow/src/arrow/python/helpers.h,sha256=uBgiOl8b-gklCa4AxKOA-8HFZkKN9WQy7n1Pa0qIAA0,5651
pyarrow/src/arrow/python/inference.cc,sha256=19SiHzwXBQBaqM3eX_Lmbwz2EP0gSX0aZdHJ973NhPA,25095
pyarrow/src/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/src/arrow/python/io.cc,sha256=ezdgnbs-wM492lG3YE1Sgb3awDjj93l4bqVMnFmAZGI,12323
pyarrow/src/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/src/arrow/python/ipc.cc,sha256=0j0rCBxCObGLNKEksCFxnuLqJVtscnpMeOS_kyT7Jus,4607
pyarrow/src/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/src/arrow/python/iterators.h,sha256=9aFm6h4iTZArpr0jSCx8PNIpzmPhmHgZENio6Ykyx6w,7527
pyarrow/src/arrow/python/numpy_convert.cc,sha256=UOhnPx7spEi2Hlq0PJz8FaiEcsCZ8m8xPoNsoLVJ6Xs,21757
pyarrow/src/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/src/arrow/python/numpy_init.cc,sha256=539UpFy5xAvGyZ9OzjHRzG4bukjaFEcMmSeATD2Rq48,1211
pyarrow/src/arrow/python/numpy_init.h,sha256=e4zEoclXjOzRydJi6iEa0SQZoBL2xax_odZ2JApkKlk,1026
pyarrow/src/arrow/python/numpy_internal.h,sha256=Cj4xT6fnW_9rYEArDQPnN3i-adOZUCm0-wfqPH1USTg,5509
pyarrow/src/arrow/python/numpy_interop.h,sha256=NMBHwZjVHiOGujS-223OxV9V5l0niyHX3-Qw1MZ-4kU,3521
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=oVehZskpFmdWKE7Z4c5_E6pGZLhmC0tcLkwccIOk0nI,32882
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RFWN7nVzsScOor6yQWkGHK9YCeDIXOrHa4bGBdyNGBE,3665
pyarrow/src/arrow/python/parquet_encryption.h,sha256=2d1-5CgmUZOFIXscD4J2nxuOkEhac0opO0PB3Jp2uz4,4993
pyarrow/src/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/src/arrow/python/platform.h,sha256=Otng5ebgyLptzmXIjS9o7tLS71emaH1doLGpDjYDxso,1463
pyarrow/src/arrow/python/pyarrow.cc,sha256=0xL6yyKbEsll_71rP0wkOLjpdnfMWaY0Ogg9S1XS58E,3777
pyarrow/src/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/src/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/src/arrow/python/python_test.cc,sha256=e4Im0nsVZNxB4U26qJvvuS2tpmtz--Tnw2BwkC_Qdfo,33310
pyarrow/src/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=Gaod9MBWdFNv2LlWdgNwkmXQmKy27miVafF85NmsBtE,48833
pyarrow/src/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/src/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/src/arrow/python/udf.cc,sha256=O_gHvu22zePxv0Qo6iThGXyjE0nwJF-ZYkka2Z0-9Io,30521
pyarrow/src/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/src/arrow/python/vendored/CMakeLists.txt,sha256=6MYldTCwSTzrxbHLzePRpUDdlKCJTBqLUd92X3aioOU,855
pyarrow/src/arrow/python/vendored/pythoncapi_compat.h,sha256=5TMOeKnV-Fpx4R1WeDeJz-3EvvLWyrgGZyCLNMTVVqs,42419
pyarrow/src/arrow/python/visibility.h,sha256=yIEoKC8dabjDOxjUdLB6CiP82nhFkyGdudhLyJNgmvU,1420
pyarrow/substrait.py,sha256=axTfPnyS2kGd2w6I1mJIAFSHSGlcY47zBF-S_Gv1a8c,1263
pyarrow/table.pxi,sha256=s9qNQibzyl47Ds6Jay2ZqS4-z2Y0GPBUOEusqbFAXDU,214477
pyarrow/tensor.pxi,sha256=UuGLCrbyv-4IU3pEnjuw1zz8V9E6ZaFbK1BhMLlzqkA,43382
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/arrow_16597.py,sha256=4MtaNkHRXf-ae8UPc_W0Sy7PQOSA38L2znT2LE0YSxM,1391
pyarrow/tests/arrow_39313.py,sha256=6iDq6Ci3q_SFDjA7RtZ27cnwmlyqGQQJgzThU5YPjG8,1478
pyarrow/tests/arrow_7980.py,sha256=W3Gbd_XbB9vC87k4VDIjyJgGfUurz6PKkFJn1pNdR4I,1124
pyarrow/tests/bound_function_visit_strings.pyx,sha256=QgKbbEM5Tm7ZNP4TTiEwpSut-sP0KEE7eT3K0KgRTw8,2093
pyarrow/tests/conftest.py,sha256=ZmkpQtnODbJBUq7nBXJMHlMRtCJv55lEJSNcMMlfpMU,10831
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=6J34Nh2eK930SY4vBkZbTlTnyC7ImMmVuJOFRIY8yDI,954
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/extensions.pyx,sha256=xcPTWeWo0rahlcDls1uh82W9BocsAmkZb34qkW5xPms,3148
pyarrow/tests/interchange/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/tests/interchange/test_conversion.py,sha256=CRhbFuV4WanciMhwvZSxYgphKenATeMx3yS1tppCK30,19138
pyarrow/tests/interchange/test_interchange_spec.py,sha256=KsktlVAnfEAX9lQrL55ca3Hfh6mMWognM3BRCrx9CuA,9613
pyarrow/tests/pandas_examples.py,sha256=rMH-mMWhDC9s56E9k9qH59IvN3NhhcVHR2sZaC1GNk0,5287
pyarrow/tests/pandas_threaded_import.py,sha256=TkyeVQWGZNoRUgbx39KC5ENTV8q3bMmo9wJxIIxso0Y,1473
pyarrow/tests/parquet/__init__.py,sha256=HBk5_BESLXOn2axHI96jkaiO9dkNzfXHZAUPf0sXbgM,955
pyarrow/tests/parquet/common.py,sha256=LEw84MUqS4_2l5FWSU9yTnRPeDhWh1wkboSg-b7BoVQ,6063
pyarrow/tests/parquet/conftest.py,sha256=TMLBP2ShxHY3PhmHe7fyWUxjFSCNSWTD8nHOpDQtTxY,3188
pyarrow/tests/parquet/encryption.py,sha256=0Zoz4i_eIEF7SepbogLZg8dWKprEw0IjgRmh-xu_vNk,2582
pyarrow/tests/parquet/test_basic.py,sha256=kAZlQQuC--zv1puAx_9ow2VG05zjmDO2dVf8teAY410,36758
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=gL9xcmd1IlVPXF68-pULYfryIrGcJI40NOd2btKH3mM,4010
pyarrow/tests/parquet/test_data_types.py,sha256=rgAy3fFhpVB5q0GrJtSExRqQwZP8TdwQZE6AYeZnYog,17021
pyarrow/tests/parquet/test_dataset.py,sha256=T4vvDX6MwikbBAEnCM--SVEnzZURsKeXUIZXsE-cdZE,43556
pyarrow/tests/parquet/test_datetime.py,sha256=g8vMoLGABOs3MgRm1-Pm3QW8fEkRHV5EMW3NXTaIr3Q,16859
pyarrow/tests/parquet/test_encryption.py,sha256=Szv05z9MC5BrHc7ufYOj4FtmCuuut_o2Rhu0D2T6Yc4,22671
pyarrow/tests/parquet/test_metadata.py,sha256=NBu2vuHzCFHTBPf87NN9t2otQa8ZCVc2frDW1pP1cLE,28558
pyarrow/tests/parquet/test_pandas.py,sha256=x3bQtGYWiwn49J8WphqbB_ne5cQ5QddGcR2zVkejxn8,23337
pyarrow/tests/parquet/test_parquet_file.py,sha256=pst0IwZ64zhso4h5TZ-eZW5yBKs2t2P-jOsAew9292Y,11395
pyarrow/tests/parquet/test_parquet_writer.py,sha256=HQINViOYlxCMfwacXHVlczHN9Y_GbEBDMo5Rp56AlLo,12096
pyarrow/tests/pyarrow_cython_example.pyx,sha256=Qgd7BEHzrxDVpdMzLplpD-XAkqN0Vu6-N75KGhLXkpQ,2176
pyarrow/tests/read_record_batch.py,sha256=J8MvpGBAv7rjvoseM5OnQyU9BL_3f2Ffdc0cYFvLh60,978
pyarrow/tests/strategies.py,sha256=eO1-NPpT8VnEZZ9s7aPJ4O_V5qOme_16Bi_3FOwY_eg,14699
pyarrow/tests/test_acero.py,sha256=b8a7xEVrlUw4GMFEdJmmaoUAziFMz7ud90Zfz3d2_F8,15416
pyarrow/tests/test_adhoc_memory_leak.py,sha256=AjG44vFpuJfzGoIT58TmFFkncVdBpXQJzLJkEokcbNg,1499
pyarrow/tests/test_array.py,sha256=XOgojEnZ2SgvsZ7OwvIz3skkdCx0iEPHNXiWA_wmpk8,145931
pyarrow/tests/test_builder.py,sha256=tLnE5vjV_DKUNhMs97s2BHjHKhgkwUSE4yMBJwgxniQ,2888
pyarrow/tests/test_cffi.py,sha256=8elpKAxXYZQEe5teru3cOciMr0QHu0RJv1of5mbwO0Q,27156
pyarrow/tests/test_compute.py,sha256=-sDmXr4_SSlpwkjz0DcHL7_YJ1_v3eUeUFyMwS9Humw,152622
pyarrow/tests/test_convert_builtin.py,sha256=4GpDLXApVjTPuRdZFGX3e6WVRp26ocJkGSxIn9urV6I,83571
pyarrow/tests/test_cpp_internals.py,sha256=IDCFZdVZRXwsu-brGSI1wYgvkf1QHlzx271UGQYAyFE,2064
pyarrow/tests/test_csv.py,sha256=Lkr83Pa-yPSRQmfZI6QC3uvsNAcIMQxn1-dl1XisEck,79359
pyarrow/tests/test_cuda.py,sha256=7LpOJ8hFXRzhNGqjz5A7t1V6fhA_1djglCjPfyMXpNM,37192
pyarrow/tests/test_cuda_numba_interop.py,sha256=zrd4EqaL3jA4R1CNqXEOVJt5TyX1a-gi_vitoJ1o364,9032
pyarrow/tests/test_cython.py,sha256=pQCix9B72QnEmSCp99xx3CTP_K1F_DZdPbaEfu1Ko9k,7319
pyarrow/tests/test_dataset.py,sha256=C4CtTZwGMeV1x8J4aIYrh83cXHiSh4az0uTjkgDBMN4,218671
pyarrow/tests/test_dataset_encryption.py,sha256=ykxgeSMm3FijNtWfIJDFjSfotd0HQs-PGEl6MMaca_Q,7825
pyarrow/tests/test_deprecations.py,sha256=qGsfYqoc2Ty5l3fIKL7zCM_IeGFgquPPv_5OWRAA6UM,914
pyarrow/tests/test_device.py,sha256=E1ElShO0Q_cBs63mtcVEsD3ARxN2c-Z2su1fJX-U2yU,2619
pyarrow/tests/test_dlpack.py,sha256=rLBFqup8I938VOgq6b4yMejOYBbDkfdkYcYTjaezyIc,5120
pyarrow/tests/test_exec_plan.py,sha256=bdEJdN7i5hc6qRKA-GTt5p9KHRqrsfaGjZCQL_XB9Qw,10433
pyarrow/tests/test_extension_type.py,sha256=LSVQ5tYKI51Wf8EY-Hw1OWGACmoe9DqQPNAHK0G8kis,69428
pyarrow/tests/test_feather.py,sha256=ZAssZGViB2cqPMkUZxOrlY51XULm2vwCW32hspudJO8,26110
pyarrow/tests/test_flight.py,sha256=MCAFNQxepfsecSTo-3EuV7Ipe6R99G2bZaudFJ04f6g,94844
pyarrow/tests/test_flight_async.py,sha256=6voht5atX8oC2fZv18LFAnoR2695_ip8zAkFiuiNqqs,2939
pyarrow/tests/test_fs.py,sha256=phOv1qV7xOj4FF6FdQTfZZeLMGB6wv80k566oBEOJGs,68124
pyarrow/tests/test_gandiva.py,sha256=9DgDgJyjl7EnXAMYBgOqABUbCeDc723W6lmLmfzft84,16057
pyarrow/tests/test_gdb.py,sha256=suiTzWyh_8ZJH1URl3bcnU2pgPwqPdgqVKFeCZCLEcs,46020
pyarrow/tests/test_io.py,sha256=r75h7ho6z52Ex_CNHOuPG9-lG2s7mt5PJv4z52jIfPw,65980
pyarrow/tests/test_ipc.py,sha256=0_m3qm_-kRoHuapQisKYXjU7hwE0VIiKPUs91xr6aeQ,44065
pyarrow/tests/test_json.py,sha256=U0W3UUxUMwebYH_GRx4pyGDcPbsR13yO4cs_-UGkP-0,23624
pyarrow/tests/test_jvm.py,sha256=IahX2XEcMmktktfWhak8eAjMMBit3JSJY5A6i4QYt3E,15942
pyarrow/tests/test_memory.py,sha256=ADbzKXGNklMPQP6nOWahZZTCs61igudOdZ54e7opI5Y,10156
pyarrow/tests/test_misc.py,sha256=4BzM6n4X_mKjhaZYIk2_8B1sIE8jl_toF7XhYa1y0us,7591
pyarrow/tests/test_orc.py,sha256=5wUhv8IKgCzwkaoKio_TlMpiv6OTIYXWTgiqwmnFTuc,21877
pyarrow/tests/test_pandas.py,sha256=AZPylaV9aJCeoYQZ4KesEJRKUvYYev3if3RQdj-BrtY,198409
pyarrow/tests/test_scalars.py,sha256=KfHX8FZuhijSksxpg7hM1xH5JDXejdz70vR_rKeRAAE,29392
pyarrow/tests/test_schema.py,sha256=wKLC2LZcMk8p7IblXAmZhi9IWpd_0gLa3FcfkJA5u9A,22788
pyarrow/tests/test_sparse_tensor.py,sha256=e6Mh1HJFPgytJM6_D1pTNP7ZIVhhFvagKQBDDs71z3Q,17994
pyarrow/tests/test_strategies.py,sha256=TS8toe1hmC2OJ2ha_89B0hDg9eP9TtMnJorytLO1tkE,1877
pyarrow/tests/test_substrait.py,sha256=b7ANCBZiTEbGJOcCVRtaNg1pfFIUxd_vc0Et2R6_RZ4,33343
pyarrow/tests/test_table.py,sha256=PdMROMvtnT_Z4kGboi4OSb2Ac6J2dVnxOnPGCdgHgK4,127501
pyarrow/tests/test_tensor.py,sha256=-Cy3GoHMC4oaqQExN9BKdN_0aHdw6ZJOV3fkpdeF5Fg,6869
pyarrow/tests/test_types.py,sha256=gqHzQYXE5t0n9k0-pdZNvVAQgYEauLhNOcwMcACCfG0,45285
pyarrow/tests/test_udf.py,sha256=7tl7W8Hur8lpeD0TcPQyY_MNHP95DB1osYgUzwVwbpY,30703
pyarrow/tests/test_util.py,sha256=NAENcrCOT9uTSpoU8KqW67e1H6tJP5COkGlAZeAAX48,5259
pyarrow/tests/test_without_numpy.py,sha256=69gobJK2_fq37P_ffFG1tgRIiopjqMY2MrOiPGOlYvQ,1913
pyarrow/tests/util.py,sha256=pR1ZfiGIDPCb0siqlGL_6tQ9WFLHWemvpBUo2thtj1I,13988
pyarrow/tests/wsgi_examples.py,sha256=uLr0kLfVuPdMVl5wY-0_sT-lDlFJhEbT5zqbIVKAK0A,1383
pyarrow/types.pxi,sha256=2seWcicBIRrBlNcFQaIcfoiZ2l3NsEkSiDGGWibBk-o,172869
pyarrow/types.py,sha256=AaFh8ZjGdF-FYLKx-agP0hlmkPXSGn2BThBy5Nzl4uU,7817
pyarrow/util.py,sha256=RdjQYJUT8_08Sf45kyTHtuMFks1uoH2nmIUGRCU0jFY,8916
pyarrow/vendored/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/vendored/docscrape.py,sha256=QgFpJRG3lyDx6Q0sUuqkkowQ23up97bT_LlgkqG5K40,23691
pyarrow/vendored/version.py,sha256=cr57fm516rcswYHhTfyRDXt-yDWq1EAlRTrZz3mVHo8,14890
