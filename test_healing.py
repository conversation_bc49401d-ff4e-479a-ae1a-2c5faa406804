"""
测试疗愈路径生成器
"""

from midnight_echo.healing_generator import <PERSON>aling<PERSON>enerator
from midnight_echo.emotion_analyzer import EmotionA<PERSON>yzer

def test_healing_generator():
    generator = HealingGenerator()
    analyzer = EmotionAnalyzer()
    
    # 测试不同情感的疗愈路径
    test_texts = [
        "我今天很开心，阳光很好",
        "我感到很难过，什么都不想做",
        "我很愤怒，这太不公平了",
        "我有点焦虑，不知道该怎么办"
    ]
    
    for text in test_texts:
        print(f"\n{'='*50}")
        print(f"文本: {text}")
        
        # 分析情感
        emotion_analysis = analyzer.analyze_emotion(text)
        print(f"情感分析: {emotion_analysis['dominant_emotion']} ({emotion_analysis['emotion_score']:.2f})")
        
        # 生成疗愈路径
        healing_path = generator.generate_healing_path(emotion_analysis)
        
        print(f"疗愈类型: {healing_path['type']}")
        print(f"推荐音乐: {healing_path['music']['title']} - {healing_path['music']['artist']}")
        print(f"冥想指导: {healing_path['meditation'][:50]}...")
        print(f"励志语句: {healing_path['quote'][:50]}...")
        print(f"建议活动: {healing_path['activity'][:50]}...")
        print(f"预计时长: {healing_path['duration']}分钟")
        print(f"视觉主题: {healing_path['visual']['theme']}")
        print(f"疗愈步骤数量: {len(healing_path['steps'])}")

if __name__ == "__main__":
    test_healing_generator()
