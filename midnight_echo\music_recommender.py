"""
音乐推荐模块
基于情感分析推荐音乐
"""

import random
from typing import Dict, List
import requests
import logging

logger = logging.getLogger(__name__)

class MusicRecommender:
    """音乐推荐器"""
    
    def __init__(self):
        """初始化音乐推荐器"""
        self.music_database = self._load_music_database()
    
    def _load_music_database(self) -> Dict:
        """加载音乐数据库"""
        return {
            'sad': [
                {
                    'title': '月光奏鸣曲',
                    'artist': '贝多芬',
                    'genre': '古典',
                    'mood': '宁静',
                    'duration': '15:00',
                    'description': '贝多芬最著名的钢琴奏鸣曲之一，月光般的旋律能够抚慰受伤的心灵',
                    'youtube_search': 'Beethoven Moonlight Sonata',
                    'spotify_search': 'Moonlight Sonata Beethoven'
                },
                {
                    'title': 'Mad World',
                    'artist': '<PERSON>',
                    'genre': '氛围',
                    'mood': '深沉',
                    'duration': '3:07',
                    'description': '深沉而富有情感的翻唱版本，适合在低落时聆听',
                    'youtube_search': 'Mad World Gary Jules',
                    'spotify_search': 'Mad World Gary Jules'
                },
                {
                    'title': '夜的钢琴曲五',
                    'artist': '石进',
                    'genre': '钢琴',
                    'mood': '治愈',
                    'duration': '4:32',
                    'description': '中国现代钢琴曲，简单而深情的旋律带来内心的平静',
                    'youtube_search': '夜的钢琴曲五 石进',
                    'spotify_search': 'Night Piano Music Shi Jin'
                },
                {
                    'title': 'Spiegel im Spiegel',
                    'artist': 'Arvo Pärt',
                    'genre': '现代古典',
                    'mood': '纯净',
                    'duration': '8:00',
                    'description': '极简主义作品，钢琴与小提琴的对话，纯净而深刻',
                    'youtube_search': 'Spiegel im Spiegel Arvo Part',
                    'spotify_search': 'Spiegel im Spiegel Arvo Part'
                }
            ],
            'angry': [
                {
                    'title': '雨声',
                    'artist': '自然音效',
                    'genre': '自然',
                    'mood': '平静',
                    'duration': '60:00',
                    'description': '自然雨声能够平息内心的怒火，带来宁静的感受',
                    'youtube_search': 'Rain Sounds for Sleep',
                    'spotify_search': 'Rain Sounds Nature'
                },
                {
                    'title': 'Weightless',
                    'artist': 'Marconi Union',
                    'genre': '氛围',
                    'mood': '放松',
                    'duration': '8:08',
                    'description': '科学证明能够减少焦虑的音乐，专门设计用于放松',
                    'youtube_search': 'Weightless Marconi Union',
                    'spotify_search': 'Weightless Marconi Union'
                },
                {
                    'title': '禅修音乐',
                    'artist': '冥想大师',
                    'genre': '冥想',
                    'mood': '宁静',
                    'duration': '30:00',
                    'description': '传统禅修音乐，帮助平息内心的波澜',
                    'youtube_search': 'Zen Meditation Music',
                    'spotify_search': 'Zen Meditation Music'
                },
                {
                    'title': 'Claire de Lune',
                    'artist': '德彪西',
                    'genre': '古典',
                    'mood': '优雅',
                    'duration': '5:00',
                    'description': '德彪西的印象派杰作，如月光般温柔的旋律',
                    'youtube_search': 'Claire de Lune Debussy',
                    'spotify_search': 'Clair de Lune Debussy'
                }
            ],
            'anxious': [
                {
                    'title': '海浪声',
                    'artist': '自然音效',
                    'genre': '自然',
                    'mood': '舒缓',
                    'duration': '60:00',
                    'description': '海浪的节奏能够调节呼吸，缓解焦虑情绪',
                    'youtube_search': 'Ocean Waves Sounds',
                    'spotify_search': 'Ocean Waves Nature Sounds'
                },
                {
                    'title': 'Deep Jungle Walk',
                    'artist': 'Astrix',
                    'genre': '氛围',
                    'mood': '沉浸',
                    'duration': '7:30',
                    'description': '深度氛围音乐，帮助转移注意力，减少焦虑',
                    'youtube_search': 'Deep Jungle Walk Astrix',
                    'spotify_search': 'Deep Jungle Walk Astrix'
                },
                {
                    'title': '森林晨曲',
                    'artist': '自然录音',
                    'genre': '自然',
                    'mood': '清新',
                    'duration': '45:00',
                    'description': '清晨森林的鸟鸣声，带来新鲜和希望的感觉',
                    'youtube_search': 'Forest Morning Birds',
                    'spotify_search': 'Forest Morning Nature'
                },
                {
                    'title': 'Gymnopédie No.1',
                    'artist': 'Erik Satie',
                    'genre': '古典',
                    'mood': '简约',
                    'duration': '3:30',
                    'description': '萨蒂的极简主义作品，简单而深刻的美',
                    'youtube_search': 'Gymnopedie No 1 Erik Satie',
                    'spotify_search': 'Gymnopedie No 1 Erik Satie'
                }
            ],
            'lonely': [
                {
                    'title': 'The Blue Notebooks',
                    'artist': 'Max Richter',
                    'genre': '现代古典',
                    'mood': '温暖',
                    'duration': '58:00',
                    'description': '现代古典音乐杰作，在孤独中寻找温暖和连接',
                    'youtube_search': 'The Blue Notebooks Max Richter',
                    'spotify_search': 'The Blue Notebooks Max Richter'
                },
                {
                    'title': '星空',
                    'artist': '二胡独奏',
                    'genre': '民族',
                    'mood': '深情',
                    'duration': '6:00',
                    'description': '二胡演奏的经典曲目，深情而富有感染力',
                    'youtube_search': '星空 二胡',
                    'spotify_search': 'Starry Sky Erhu'
                },
                {
                    'title': 'Elegy for Dunkirk',
                    'artist': 'Dario Marianelli',
                    'genre': '电影配乐',
                    'mood': '史诗',
                    'duration': '1:27',
                    'description': '电影《赎罪》配乐，在孤独中感受人性的光辉',
                    'youtube_search': 'Elegy for Dunkirk Atonement',
                    'spotify_search': 'Elegy for Dunkirk Dario Marianelli'
                },
                {
                    'title': '夜莺',
                    'artist': '古筝演奏',
                    'genre': '民族',
                    'mood': '优美',
                    'duration': '5:30',
                    'description': '古筝演奏的经典曲目，如夜莺般优美的旋律',
                    'youtube_search': '夜莺 古筝',
                    'spotify_search': 'Nightingale Guzheng'
                }
            ],
            'happy': [
                {
                    'title': 'Nuvole Bianche',
                    'artist': 'Ludovico Einaudi',
                    'genre': '现代古典',
                    'mood': '升华',
                    'duration': '5:57',
                    'description': '意大利作曲家的代表作，如白云般纯净美好',
                    'youtube_search': 'Nuvole Bianche Ludovico Einaudi',
                    'spotify_search': 'Nuvole Bianche Ludovico Einaudi'
                },
                {
                    'title': '春天的故事',
                    'artist': '钢琴版',
                    'genre': '钢琴',
                    'mood': '温暖',
                    'duration': '4:00',
                    'description': '经典歌曲的钢琴演奏版，充满希望和温暖',
                    'youtube_search': '春天的故事 钢琴',
                    'spotify_search': 'Spring Story Piano'
                },
                {
                    'title': "Comptine d'un autre été",
                    'artist': 'Yann Tiersen',
                    'genre': '电影配乐',
                    'mood': '浪漫',
                    'duration': '2:18',
                    'description': '电影《天使爱美丽》配乐，充满法式浪漫情怀',
                    'youtube_search': 'Comptine autre ete Yann Tiersen',
                    'spotify_search': 'Comptine autre ete Yann Tiersen'
                },
                {
                    'title': '阳光总在风雨后',
                    'artist': '纯音乐版',
                    'genre': '励志',
                    'mood': '希望',
                    'duration': '4:30',
                    'description': '励志歌曲的纯音乐版本，传递希望和正能量',
                    'youtube_search': '阳光总在风雨后 纯音乐',
                    'spotify_search': 'Sunshine After Rain Instrumental'
                }
            ],
            'tired': [
                {
                    'title': 'Sleep Baby Sleep',
                    'artist': 'Broods',
                    'genre': '电子',
                    'mood': '舒缓',
                    'duration': '4:12',
                    'description': '温柔的电子音乐，帮助放松和恢复精力',
                    'youtube_search': 'Sleep Baby Sleep Broods',
                    'spotify_search': 'Sleep Baby Sleep Broods'
                },
                {
                    'title': '摇篮曲',
                    'artist': '勃拉姆斯',
                    'genre': '古典',
                    'mood': '安详',
                    'duration': '2:00',
                    'description': '经典摇篮曲，带来母亲般的温暖和安全感',
                    'youtube_search': 'Brahms Lullaby',
                    'spotify_search': 'Brahms Lullaby'
                }
            ]
        }
    
    def recommend_music(self, emotion: str, count: int = 1) -> List[Dict]:
        """
        根据情感推荐音乐
        
        Args:
            emotion: 情感类型
            count: 推荐数量
            
        Returns:
            推荐的音乐列表
        """
        if emotion not in self.music_database:
            emotion = 'sad'  # 默认情感
        
        music_list = self.music_database[emotion]
        
        if count >= len(music_list):
            return music_list
        else:
            return random.sample(music_list, count)
    
    def get_music_by_mood(self, mood: str) -> List[Dict]:
        """根据心境获取音乐"""
        matching_music = []
        
        for emotion_music in self.music_database.values():
            for music in emotion_music:
                if music['mood'].lower() == mood.lower():
                    matching_music.append(music)
        
        return matching_music
    
    def search_music(self, query: str) -> List[Dict]:
        """搜索音乐"""
        query = query.lower()
        matching_music = []
        
        for emotion_music in self.music_database.values():
            for music in emotion_music:
                if (query in music['title'].lower() or 
                    query in music['artist'].lower() or
                    query in music['genre'].lower() or
                    query in music['description'].lower()):
                    matching_music.append(music)
        
        return matching_music
    
    def get_playlist_for_healing(self, healing_type: str) -> List[Dict]:
        """为特定疗愈类型生成播放列表"""
        playlist_mapping = {
            'deep_healing': ['sad', 'lonely'],
            'comfort': ['sad', 'tired'],
            'calming': ['angry', 'anxious'],
            'energizing': ['happy'],
            'clarity': ['anxious', 'lonely']
        }
        
        emotions = playlist_mapping.get(healing_type, ['sad'])
        playlist = []
        
        for emotion in emotions:
            music_list = self.recommend_music(emotion, 2)
            playlist.extend(music_list)
        
        return playlist
    
    def generate_music_links(self, music: Dict) -> Dict:
        """生成音乐链接"""
        youtube_base = "https://www.youtube.com/results?search_query="
        spotify_base = "https://open.spotify.com/search/"
        
        youtube_query = music['youtube_search'].replace(' ', '+')
        spotify_query = music['spotify_search'].replace(' ', '%20')
        
        return {
            'youtube': f"{youtube_base}{youtube_query}",
            'spotify': f"{spotify_base}{spotify_query}",
            'apple_music': f"https://music.apple.com/search?term={youtube_query}",
            'netease': f"https://music.163.com/#/search/m/?s={music['title']}+{music['artist']}"
        }
    
    def get_music_recommendations_with_links(self, emotion: str, count: int = 3) -> List[Dict]:
        """获取带链接的音乐推荐"""
        recommendations = self.recommend_music(emotion, count)
        
        for music in recommendations:
            music['links'] = self.generate_music_links(music)
        
        return recommendations
